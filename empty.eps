%!PS-Adobe-3.0
%%Pages: (atend)
%%BoundingBox: 0 0 115 87
%%HiResBoundingBox: 0.563794 0.527295 114.418213 86.036206
%...........................................
%%Creator: GNU Ghostscript 653 (pswrite)
%%CreationDate: 2002/07/10 13:09:14
%%DocumentData: Clean7Bit
%%LanguageLevel: 2
%%EndComments
%%BeginProlog
% This copyright applies to everything between here and the %%EndProlog:
% Copyright (C) 2002 artofcode LLC, Benicia, CA. All rights reserved.
%%BeginResource: procset GS_pswrite_2_0_1001
/GS_pswrite_2_0_1001 80 dict dup begin
/PageSize 2 array def/setpagesize{ PageSize aload pop 3 index eq exch
4 index eq and{ pop pop pop}{ PageSize dup  1
5 -1 roll put 0 4 -1 roll put dup where{ exch get exec}
{ pop/setpagedevice where
{ pop 1 dict dup /PageSize PageSize put setpagedevice}
{ /setpage where{ pop PageSize aload pop pageparams 3 {exch pop} repeat
setpage}if}ifelse}ifelse}ifelse} bind def
/!{bind def}bind def/#{load def}!/N/counttomark #
/rG{3{3 -1 roll 255 div}repeat setrgbcolor}!/G{255 div setgray}!/K{0 G}!
/r6{dup 3 -1 roll rG}!/r5{dup 3 1 roll rG}!/r3{dup rG}!
/w/setlinewidth #/J/setlinecap #
/j/setlinejoin #/M/setmiterlimit #/d/setdash #/i/setflat #
/m/moveto #/l/lineto #/c/rcurveto #
/p{N 2 idiv{N -2 roll rlineto}repeat}!
/P{N 0 gt{N -2 roll moveto p}if}!
/h{p closepath}!/H{P closepath}!
/lx{0 rlineto}!/ly{0 exch rlineto}!/v{0 0 6 2 roll c}!/y{2 copy c}!
/re{4 -2 roll m exch dup lx exch ly neg lx h}!
/^{3 index neg 3 index neg}!
/f{P fill}!/f*{P eofill}!/s{H stroke}!/S{P stroke}!
/q/gsave #/Q/grestore #/rf{re fill}!
/Y{P clip newpath}!/Y*{P eoclip newpath}!/rY{re Y}!
/|={pop exch 4 1 roll 3 array astore cvx exch 1 index def exec}!
/|{exch string readstring |=}!
/+{dup type/nametype eq{2 index 7 add -3 bitshift 2 index mul}if}!
/@/currentfile #/${+ @ |}!
/B{{2 copy string{readstring pop}aload pop 4 array astore cvx
3 1 roll}repeat pop pop true}!
/Ix{[1 0 0 1 11 -2 roll exch neg exch neg]exch}!
/,{true exch Ix imagemask}!/If{false exch Ix imagemask}!/I{exch Ix image}!
/Ic{exch Ix false 3 colorimage}!
/F{/Columns counttomark 3 add -2 roll/Rows exch/K -1/BlackIs1 true>>
/CCITTFaxDecode filter}!/FX{<</EndOfBlock false F}!
/X{/ASCII85Decode filter}!/@X{@ X}!/&2{2 index 2 index}!
/@F{@ &2<<F}!/@C{@X &2 FX}!
/$X{+ @X |}!/&4{4 index 4 index}!/$F{+ @ &4<<F |}!/$C{+ @X &4 FX |}!
/IC{3 1 roll 10 dict begin 1{/ImageType/Interpolate/Decode/DataSource
/ImageMatrix/BitsPerComponent/Height/Width}{exch def}forall
currentdict end image}!
end readonly def
%%EndResource
/pagesave null def
%%EndProlog
%%Page: 1 1
%%BeginPageSetup
GS_pswrite_2_0_1001 begin
595 842 /a4 setpagesize
/pagesave save store 100 dict begin
0.1 0.1 scale
%%EndPageSetup
gsave mark
Q q
0 0 1150 870 re
Y
4.72425 w
K
8 7.63501 1133.82 850.365 re
S
8 858 1133.82 -850.36 S
8 7.64 1133.82 850.36 S
cleartomark end end pagesave restore showpage
%%PageTrailer
%%Trailer
%%Pages: 1
%%EOF
