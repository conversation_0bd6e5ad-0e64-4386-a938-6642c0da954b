This is BibTeX, Version 0.99d (TeX Live 2024)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: sn-article.aux
The style file: sn-mathphys-num.bst
Reallocated glb_str_ptr (elt_size=4) to 20 items from 10.
Reallocated global_strs (elt_size=200001) to 20 items from 10.
Reallocated glb_str_end (elt_size=4) to 20 items from 10.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 6000 items from 3000.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Database file #1: sn-bibliography.bib
sn-mathphys-num.bst [2024/07/19 v1.1  bibliography style]
Reallocated wiz_functions (elt_size=4) to 9000 items from 6000.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
You've used 32 entries,
            7485 wiz_defined-function locations,
            1385 strings with 16803 characters,
and the built_in function-call counts, 234966 in all, are:
= -- 37468
> -- 1254
< -- 523
+ -- 17933
- -- 348
* -- 10023
:= -- 43273
add.period$ -- 34
call.type$ -- 32
change.case$ -- 202
chr.to.int$ -- 1424
cite$ -- 32
duplicate$ -- 1635
empty$ -- 8151
format.name$ -- 955
if$ -- 57768
int.to.chr$ -- 1
int.to.str$ -- 54
missing$ -- 0
newline$ -- 534
num.names$ -- 160
pop$ -- 855
preamble$ -- 1
purify$ -- 149
quote$ -- 0
skip$ -- 22189
stack$ -- 0
substring$ -- 25586
swap$ -- 918
text.length$ -- 386
text.prefix$ -- 0
top$ -- 1
type$ -- 1333
warning$ -- 0
while$ -- 1020
width$ -- 0
write$ -- 724
