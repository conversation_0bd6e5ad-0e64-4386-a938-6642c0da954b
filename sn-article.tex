%Version 3.1 December 2024
% See section 11 of the User Manual for version history
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%                                                                 %%
%% Please do not use \input{...} to include other tex files.       %%
%% Submit your LaTeX manuscript as one .tex document.              %%
%%                                                                 %%
%% All additional figures and files should be attached             %%
%% separately and not embedded in the \TeX\ document itself.       %%
%%                                                                 %%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%%\documentclass[referee,sn-basic]{sn-jnl}% referee option is meant for double line spacing

%%=======================================================%%
%% to print line numbers in the margin use lineno option %%
%%=======================================================%%

%%\documentclass[lineno,pdflatex,sn-basic]{sn-jnl}% Basic Springer Nature Reference Style/Chemistry Reference Style

%%=========================================================================================%%
%% the documentclass is set to pdflatex as default. You can delete it if not appropriate.  %%
%%=========================================================================================%%

%%\documentclass[sn-basic]{sn-jnl}% Basic Springer Nature Reference Style/Chemistry Reference Style

%%Note: the following reference styles support Namedate and Numbered referencing. By default the style follows the most common style. To switch between the options you can add or remove �Numbered� in the optional parenthesis. 
%%The option is available for: sn-basic.bst, sn-chicago.bst%  
 
%%\documentclass[pdflatex,sn-nature]{sn-jnl}% Style for submissions to Nature Portfolio journals
%%\documentclass[pdflatex,sn-basic]{sn-jnl}% Basic Springer Nature Reference Style/Chemistry Reference Style
\documentclass[iicol,pdflatex,sn-mathphys-num]{sn-jnl}% Math and Physical Sciences Numbered Reference Style
%%\documentclass[pdflatex,sn-mathphys-ay]{sn-jnl}% Math and Physical Sciences Author Year Reference Style
%%\documentclass[pdflatex,sn-aps]{sn-jnl}% American Physical Society (APS) Reference Style
%%\documentclass[pdflatex,sn-vancouver-num]{sn-jnl}% Vancouver Numbered Reference Style
%%\documentclass[pdflatex,sn-vancouver-ay]{sn-jnl}% Vancouver Author Year Reference Style
%%\documentclass[pdflatex,sn-apa]{sn-jnl}% APA Reference Style
%%\documentclass[pdflatex,sn-chicago]{sn-jnl}% Chicago-based Humanities Reference Style

%%%% Standard Packages
%%<additional latex packages if required can be included here>

\usepackage{graphicx}%
\usepackage{multirow}%
\usepackage{amsmath,amssymb,amsfonts}%
\usepackage{amsthm}%
\usepackage{mathrsfs}%
\usepackage[title]{appendix}%
\usepackage{xcolor}%
\usepackage{textcomp}%
\usepackage{manyfoot}%
\usepackage{booktabs}%
\usepackage{algorithm}%
\usepackage{algorithmicx}%
\usepackage{algpseudocode}%
\usepackage{listings}%
%%%%

%%%%%=============================================================================%%%%
%%%%  Remarks: This template is provided to aid authors with the preparation
%%%%  of original research articles intended for submission to journals published 
%%%%  by Springer Nature. The guidance has been prepared in partnership with 
%%%%  production teams to conform to Springer Nature technical requirements. 
%%%%  Editorial and presentation requirements differ among journal portfolios and 
%%%%  research disciplines. You may find sections in this template are irrelevant 
%%%%  to your work and are empowered to omit any such section if allowed by the 
%%%%  journal you intend to submit to. The submission guidelines and policies 
%%%%  of the journal take precedence. A detailed User Manual is available in the 
%%%%  template package for technical guidance.
%%%%%=============================================================================%%%%

%% as per the requirement new theorem styles can be included as shown below
\theoremstyle{thmstyleone}%
\newtheorem{theorem}{Theorem}%  meant for continuous numbers
%%\newtheorem{theorem}{Theorem}[section]% meant for sectionwise numbers
%% optional argument [theorem] produces theorem numbering sequence instead of independent numbers for Proposition
\newtheorem{proposition}[theorem]{Proposition}% 
%%\newtheorem{proposition}{Proposition}% to get separate numbers for theorem and proposition etc.

\theoremstyle{thmstyletwo}%
\newtheorem{example}{Example}%
\newtheorem{remark}{Remark}%

\theoremstyle{thmstylethree}%
\newtheorem{definition}{Definition}%

\raggedbottom
%%\unnumbered% uncomment this for unnumbered level heads

\begin{document}

\title[EEG Generation and Classification with Domain Adaptation]{EEG Generation and Classification Based on Conditional Sequence Generation and Domain Adaptive}

%%=============================================================%%
%% GivenName	-> \fnm{Joergen W.}
%% Particle	-> \spfx{van der} -> surname prefix
%% FamilyName	-> \sur{Ploeg}
%% Suffix	-> \sfx{IV}
%% \author*[1,2]{\fnm{Joergen W.} \spfx{van der} \sur{Ploeg} 
%%  \sfx{IV}}\email{<EMAIL>}
%%=============================================================%%

\author[1]{\fnm{First} \sur{Peiran Liu}}\email{<EMAIL>}

\author[1]{\fnm{Second} \sur{Lu Li}}\email{<EMAIL>}

\author*[2]{\fnm{Third} \sur{Qinkun Xiao}}\email{<EMAIL>}

\affil[1]{\orgdiv{Department of Mechanical and Electrical Engineering}, \orgname{Xi'an Technological University}, \orgaddress{\street{No.2 Xuefu Middle Road}, \city{xi'an}, \postcode{710021}, \state{Shaanxi}, \country{China}}}

\affil*[2]{\orgdiv{Department of Electrical and Information Engineering}, \orgname{Xi'an Technological University}, \orgaddress{\street{No.2 Xuefu Middle Road}, \city{xi'an}, \postcode{710021}, \state{Shaanxi}, \country{China}}}


%%==================================%%
%% Sample for unstructured abstract %%
%%==================================%%

\abstract{Human-Computer Interaction (HCI) based on electroencephalogram (EEG) has become the main research direction in the field of Brain-Computer Interface. Although many achievements have been made in EEG research in recent years, effective EEG recognition is still a challenge. To this end, this paper proposed an EEG generative and recognition model called the conditional sequence generation and domain adaptive (CSGDA) model. The highlights include: (1) Most of the existing studies on EEG analysis only consider the EEG signal as a chain sequence, but ignore the relevance and spatial-temporal relationships between brain electrode signals. To improve the BIR accuracy, this paper proposed a graph-based EEG signal representation that can reflect the temporal and spatial attribution of EEG signals. (2) Meanwhile, this paper proposed two approaches to augment the insufficient training data of EEG: one is to use the cGAN-based sequence generation model to produce more train data, and the other is to use the domain adaptive (DA) method to combine more subject data. Evaluation were conducted and the resilts show the proposed model is superior to the exsiting methods.}

\keywords{BCI, cGAN, domain adaptive, EEG, sequence generation}

\maketitle

\section{Introduction}\label{sec1}

Electroencephalography (EEG) signals are widely used in brain-computer interface (BCI) research due to their non-invasive nature and ability to capture brain activity(BCI)\cite{bib1,bib2}. However, the complex characteristics of EEG signals, such as high dimensionality\cite{bib3}\cite{bib4}, variability across subjects\cite{bib5}\cite{bib6}, and sensitivity to noise\cite{bib7}\cite{bib8}, present significant challenges for effective analysis and modeling. Traditional methods often rely on time-series representations\cite{bib9}\cite{bib10}, which lack the ability to capture the spatial relationships among electrodes, limiting their potential to fully utilize EEG data.

To address these limitations, this paper introduces a novel approach that combines graph-based representation and advanced generative models. Specifically, a graph-based representation is proposed to describe EEG signals, where the nodes represent electrodes and the edges encode their relationships. This method not only retains the temporal information of the signal but also reveals the spatial connections between electrode nodes, offering a more comprehensive understanding of EEG data.

Furthermore, we propose a cross-subject EEG recognition and generation framework named the cGAN-based sequence generation and domain adaptation (CSGDA) model. This approach leverages domain adaptation (DA) techniques to extract transferable EEG features across subjects and uses conditional generative adversarial networks (cGAN) to augment training data. This combination enhances the robustness of the model and improves its generalizability to new subjects and conditions.

The proposed method is validated on large-scale EEG datasets, covering diverse motion intention identification tasks across subjects and scenarios. Experimental results demonstrate that the CSGDA model outperforms existing approaches, achieving superior performance in terms of recognition accuracy and robustness. These findings highlight the potential of the proposed framework to advance EEG-based applications, particularly in scenarios requiring cross-subject adaptability and large-scale data analysis.

The rest is organized as follows: the method is described in Section \ref{sec2}, the evaluation is tested and analyzed in Section \ref{sec3}, and the conclusions are given in Section \ref{sec4}. 

\section{Methods}\label{sec2}

\subsection{Overview}

The proposed EEG generation and classification framework is shown in Figure~\ref{fig1}. The system consists of seven modules: (1) the GCN module ($G_{\varphi}$), (2) the BiLSTM encoder ($f_{\theta_e}$), (3) the attention module ($f_{\theta_{att}}$), (4) the decoder ($f_{\theta_d}$), (5) the domain latent variable classifier ($\mathcal{C}_{\omega}$), (6) the generation discriminator ($\mathcal{D}_\phi$), and (7) the EEG classifier ($f_{\theta_c}$).

\begin{figure*}[h]
\centering
\includegraphics[width=0.9\textwidth]{Fig1.eps}
\caption{The proposed EEG sequence generation and recognition model (CGSAD). (a) The total frameworks of proposed model.  (b) DA architecture description. Across-subject features extraction using proposed graph-based and adversarial learning.}\label{fig1}
\end{figure*}

In EEG recognition, there are three modules: $f_{\theta_e}$, $\mathcal{C}_\omega$ and $f_{\theta_c}$. Firstly, the original EEG data at time $t$ is converted into graph $\mathcal{G}$, and this paper denotes the graph sequence data from domains $A$ and $B$ as  $\mathcal{X}^S=(\mathcal{G}_1^S,\cdots,\mathcal{G}_T^S)$ and $\mathcal{X}^T=(\mathcal{G}_1^T,\cdots,\mathcal{G}_T^T)$ , respectively.  $\mathcal{X}=(\mathcal{G}_1,\cdots,\mathcal{G}_T)$ is inputted into the GCN module $G_\varphi$ and encoder $f_{\theta_e}$, i.e., ``GCN-BiLSTM" model, and the output is  a sequence feature $h_T$. Then, a latent variable classifier $\mathcal{C}_\omega$ is used to identify the domain $h_T$ comes from. Meanwhile, the $h_T$ is fed into the EEG classifier $f_{\theta_c}$ to predict the result $\hat{y}$. 

In EEG generation, the cGAN-based sequence generation involves four modules: $f_{\theta_e}$, $f_{\theta_{att}}$, $\mathcal{D}_\phi$ and $f_{\theta_d}$.  $\mathcal{X}$ is fed into $G_\varphi$, and sequence $x$ is encoded; then, the attention coefficient $\alpha$ and context vector $cv$ are calculated. Given a conditional random signal $\mathbf{z}=z+y$, where $z\sim \mathcal{N}(0,\sigma_z^2)$ and $y$ is the groundtruth label, based on the learned $cv$, the decoder $f_{\theta_d}$ can predict an EEG sequence $\mathbf{\hat{x}}^g$. To encode $\mathbf{\hat{x}}^g$ as $\hat{h}_T^g$, this paper also uses a discriminator $\mathcal{D}_\phi$ to distinguish between the real data $h_T$ and generated data $\hat{h}_T^g$.       
	
In our model, conditional sequence generation and domain adaptive modes are combined to force different domain data ($\mathbf{x}^S$, $\mathbf{x}^T$) and generated data ($\mathbf{\hat{x}}^g$) to have a unified optimal feature $h_T$. Thus, $h_T$ can reflect the essential part of EEG intention.	

In system learning, to generate the desired EEG sequence and obtain discriminable EEG features, the total loss function $\mathcal{L}_{total}$ includes the classification loss $\mathcal{L}_{class}$ , the variational loss $\mathcal{L}_D$, the adversarial generation loss $\mathcal{L}_{\phi}$, and the latent variable identification loss $\mathcal{L}_\omega$. The goal of system learning is to identify and generate high-quality, and diverse EEG sequence data. Furthermore, the generation data supplement the training data, and improve the performance of the classifier. The details are described below.

\subsection{Module Design}
\textbf{GCN extractor} ($G_\varphi$).In this paper, a graph is used to represent the EEG signal at time $t$, then, the GCN extractor is utilized to extract the feature vector of the graph.

The graph $\mathcal{G}=\{\mathcal{V}, \mathcal{E}\}$ is built as shown in Figure~\ref{fig2}. There are $n$ nodes in the graph, which correspond to $n$-channel electrode signals; each node is denoted as $\textbf{v}_t=(l_x, l_y, v_t)$, where $l_x$ and $l_y$ are the location of the node, and $v_t$ is the signal value at time $t$. Then, the set of all nodes is denoted as $\mathcal{V}_t=\{\mathbf{v}_t^i\}_{i=1}^n$. As shown in Figure~\ref{fig2}(d), a coordinate axis, is built, where the ``Cz" electrode is at the ($l_x=0,l_y=0$) point, and the other electrodes, such as the ``Fz" electrode, are at the ($l_x=0,l_y=2$) point.  As shown in Figure~\ref{fig2}(c), relation matrix $\mathcal{E}=[e_{ij}]_{n\times n}$, is calculated, and $e_{ij}$ is calculated as:
\begin{equation}
e_{ij}=\frac{\mathbf{cov}(v^i, v^j)} {\sigma_{v^i} \sigma_{v^j}}.\label{eq1}
\end{equation}
where $v^i$ is the value of the $i$th channel electrode signal,  $\mathbf{cov}(v^i,v^j)$ is the covariance between $v^i$ and $v^j$, and $\sigma_{v^i}$ is the variance of $v^i$. The MI task of subject are different each other, so the value of $\mathcal{E}$ is also different. According to the sample calculation, a mean value of $\mathcal{E}$ is shown in Figure~\ref{fig2}(c), and based on the mean matrix $\mathcal{E}$ for different tasks of a subject, the graph $\mathcal{G}$ is constructed, as shown in Fig.\ref{fig2}(d). To show the structure of $\mathcal{G}$ clearly, only part of the edges is shown. Given a graph  sequence  $\mathcal{X} = \{\mathcal{G}_1,\cdots, \mathcal{G}_T\}$,the function $G_\varphi$  can be written as:
\begin{equation}
(x_1,\cdots,x_T) = G_\varphi(\mathcal{G}_1,\cdots,\mathcal{G}_T).\label{eq2}
\end{equation}     
where the output sequence $\mathbf{x}=(x_1,\cdots, x_T)$ is the graph sequence feature.

\begin{figure}[h]
\centering
\includegraphics[width=\columnwidth]{Fig2.eps}
\caption{The proposed graph-based EEG signal representation.(a) EEG cap, (b) EEG layout map, (c) correlation matrix, (d) EEG graph representation.}\label{fig2}
\end{figure}

\textbf{Encoder ($f_{\theta_e}$)}. The module encodes the EEG sequence $\mathbf{x}=(x_1, \cdots, x_T)$ into an encoded sequence $\mathbf{h}=(h_1, \cdots, h_T)$. As shown in Fig.\ref{fig1}, $\mathbf{x}$ denotes the EEG sequence. In our system, $\mathbf{x}$ may be $\mathbf{x}^S$, $\mathbf{x}^T$, or $\mathbf{\hat{x}}^g$, where $\mathbf{x}^S$ and $\mathbf{x}^T$ denote the EEG sequence from domain $S$ and $T$, respectively.  $\mathbf{\hat{x}}^g$ denotes the generated sequence. Similar to $\mathbf{x}$, the encoded sequence $\mathbf{h}$ may be $\mathbf{h}^S$, $\mathbf{h}^T$, or $\mathbf{\hat{h}}^g$.
 
This paper uses a BiLSTM network as the encoder, and our model can effectively represent the context information. A BiLSTM unit can be calculated as:
\begin{equation}
[h_t,c_t] = U_e^{BiLSTM}(x_t,h_{t-1}).\label{eq3}
\end{equation}
where $U_e^{BiLSTM}$ is a BiLSTM unit, and $h_t$ is the encoded value at time $t$. A unit includes a forward unit and a backward unit, so the encoded vector can be expressed as $h_t=[\overleftarrow{h} _t,\overrightarrow{h}_t]$. 
When $\mathbf{x}$ is fed into the encoder $f_{\theta_e}$, we have:
\begin{equation}
(h_1, \cdots ,h_T) = f_{\theta_e}(\mathbf{x},h_0).\label{eq4}
\end{equation}
where $h_0$ is the initial given data, $\theta_e$ is the encoder parameter, and $h_T$ contains the global information of $\mathbf{x}$.

\textbf{Attention module} ($f_{\theta_{att}}$). This module calculates the attention coefficient $\alpha$ and the context vector $cv$ of the input EEG sequence. The attention mechanism is an effective tool that helps the decoder to interpret the input sequence data.

Let $h_i$ be an encoded vector of $x_i$, according to the attention mechanism, we have:
\begin{equation}
c{v_t} = \sum_{i=1}^T \alpha_{ti} h_i.\label{eq5}
\end{equation}
where $\alpha_{ti}$ is the attention weight, and $e_{ti}$ is an alignment model that aligns the hidden state variable $h_{i-1}^d$ in the decoder with the hidden state variable $h_i$ in the encoder. If $e_{ti}=\mathbf{softmax}[(h_{i-1}^d)^T h_i]$, we have:
\begin{equation}
(cv_1, \cdots, cv_T) = f_{\theta_{att}}(h_1, \cdots ,h_T).\label{eq6}
\end{equation}
where $\theta_{att}$ is a parameter.

\textbf{Decoder} ($f_{\theta_d}$). This module decodes the random variable $z$ to generate the EEG sequence $\mathbf{\hat{x}}^g=(\hat{x}_1^g,\cdots,\hat{x}_T^g)$. In this paper, cGAN-based sequence generation is adopted for data augmentation.

As shown in Figure~\ref{fig1}, $z$ is a normal random variable, to obtain the condition-based generation results, $z$ is combined with the label vector $y$ to form the input of the decoder:
\begin{equation}
\mathbf{z} = z + y, y \in {\mathbb{R}^l}.\label{eq7}
\end{equation}
where $y$ is represented by an $l$-dimensional one-hot vector, $l$ is the number of classes, and $\mathbf{\hat{x}}^g$ is the output of the decoder in our system. 

This paper uses a single-layer LSTM network as the decoder. An LSTM-based decoder unit can be calculated as:
\begin{equation}
[\hat{x}_{t}^g, h_t^d] = U_d^{LSTM}({\hat{x}_{t-1}^g}, h_{t-1}^d, c{v_t}).\label{eq8}
\end{equation}	
where $U_d^{LSTM}$ is the basic decoder unit. Then, if $h_0^d=\mathbf{z}$, the decoder calculation can be represented by: 
\begin{equation}
\mathbf{\hat{x}}^g = f_{\theta_d}(\mathbf{z} = h_0^d, cv_1, \cdots, cv_T).\label{eq9}
\end{equation} 
where $f_{\theta_d}$ is the decoder, and $\mathbf{\hat{x}}^g = (\hat{x}_1^g, \cdots, \hat{x}_T^g)$ is the generated EEG sequence. 

\textbf{Latent variable classifier} ($\mathcal{C}_\omega$).
$\mathcal{C}_\omega$ uses multilayer perception to distinguish the encoded feature $h_T$ from domain $S$ or domain $T$. 

To effectively distinguish $h^S_T$ from $h_T^T$, this paper designs a latent variable classifier $\mathcal{C}_\omega$. Specifically, for variable $h_T$, we have:
\begin{equation}\label{eq10}
\left\{ 
\begin{array}{l}
\mathcal{C}_\omega (h_T) = 1, \mathrm{if} \ h_T \in S \\ 
\mathcal{C}_\omega (h_T) = 0, \mathrm{if}  \ h_T \in T \\ 
\end{array} \right.
\end{equation}
where $\omega$ is a parameter of $\mathcal{C}_\omega$.

\textbf{Discriminator} ($\mathcal{D}_\phi$). As shown in Figure~\ref{fig1}, in the proposed model, an adversarial structure with an encoder $f_{\theta_e}$, a decoder $f_{\theta_d}$, and an adversarial discriminator $\mathcal{D}_\phi$ is used. This structure enables adversarial learning between the real data $\mathbf{x}$ and the generated data $\mathbf{\hat{x}}$,  $\mathbf{\hat{x}}$ and $\mathbf{x}$ match finally. Similar to $\mathcal{C}_\omega$, for variable $h_T$, we have:
\begin{equation}\label{eq11}
	\left\{
	 \begin{array}{l}
		\mathcal{D}_\phi (h_T) = 1, \mathrm{if} \ h_T \ \mathrm{from} \  \mathbf{x} \\ 
		\mathcal{D}_\phi (h_T) = 0, \mathrm{if}  \ h_T \ \mathrm{from}  \  \mathbf{\hat{x}} \\ 
	\end{array} \right.
\end{equation}
where $\phi$ is a parameter of $\mathcal{D}_\phi$.

\textbf{SL classifier} ($f_{\theta_c}$). As shown in Figure~\ref{fig1}, the classifier consists of two fully connected layers and a softmax layer, and it is used for EEG sequence identification. The calculation process can be represented as $\hat{y} = f_{\theta_c}({h_T})$, where $f_{\theta_c}$ is the classifier, $\hat{y}$ is the classification label vector, $\theta_c$ is the classifier parameter, and $y$ is the ground truth CSL class label. When $h_T$ is an encoded feature, it can be $h_T^S$, $h_T^T$, or $\hat{h}_T^g$, and $\hat{y}$ can be $\hat{y}^S$, $\hat{y}^T$ or $\hat{y}^g$.

\subsection{System Learning}
	
\textbf{Classification loss $\mathcal{L}_{class}$}. The classification loss function is defined as:
\begin{equation}\label{eq12}
\begin{split}
\mathcal{L}_{class} = \lambda_1 \cdot \mathcal{L}(y, \hat{y}^g)  
+ \lambda_2 \cdot \mathcal{L}(y, \hat{y}^S)
+ \lambda_3 \cdot \mathcal{L}(y, \hat{y}^T)
\end{split}
\end{equation}
where $\sum_i \lambda_i=1$ is the weighting coefficient. The loss function of each component is the cross entropy loss between the labels, which is calculated by the following formula:
\begin{eqnarray}\label{eq13}
\mathcal{L}(y,\hat{y}) = \sum_{i=1}^l  p_y(i)  \cdot \log p_{\hat{y}} (i),
\end{eqnarray}

where $l$ is the number of classes, $p_{\hat{y}(i)}$ is the prediction probability of the EEG sequence ($\mathbf{x}$) belonging to the $i$th class, and $p_{y} (i)$ is the ground truth probability.	

\textbf{Generation loss} ( $\mathcal{L}_{g}$)	
The cGAN-based sequence generation requires that $p(\mathbf{x})$ and $p(\mathbf{\hat{x}})$ is as closerly as possible. Hence, this paper uses the Kullback-Leibler divergence (KLdiv) to calculate the distance between distributions: 
\begin{equation}\label{eq14}
\mathcal{L}_g = KLdiv(p(\mathbf{x}) \parallel p(\mathbf{\hat{x}})).
\end{equation}

To obtain the optimal system parameters, the EEG sequence $\mathbf{x}$ is assumed to obey the Gaussian Mixture Model (GMM) distribution. Thus, $\mathbf{x}=(x_t)_{t=1}^T$, and $x_t=(\text{channel}_{t,i})_{i=1}^n$, where $\text{channel}_{t,i}$ is the value of the $i$th channel at time $t$, and $n$ is the number of channels. If each channel signal obeys  1-dimensional normal distribution, we have:
\begin{equation}\label{eq15}
\text{channel}_{t,i} \sim p(\text{channel}_{t,i}) = \mathcal{N}(\mu_{t,i},\sigma_{t,i}^2),
\end{equation}
where $\mu_{t,i}$ and $\sigma_{t,i}^2$ are the expectation and variance of $\text{channel}_{t,i}$, respectively. Furthermore, we have:
\begin{equation}\label{eq16}
x_t \sim p_{gmm} (x_t) = \sum_{i=1}^n \pi_i \cdot \mathcal{N} (\mu_{t,i},\sigma_{t,i}^2),
\end{equation}
where $p_{gmm} (x_t)$ is a GMM distribution and $\pi_i$ is the coefficient of the $i$th component. Then, we have:
\begin{equation}\label{eq17}
\begin{split}
\mathbf{x} \sim p_{gmm} (\mathbf{x}; \Phi) & = \sum_{t=1}^T \kappa_t \cdot p_{gmm} (x_t) 
\\
&=\sum_{t=1}^T (\kappa_t \cdot \sum_{i=1}^n (\pi_i \cdot \mathcal{N} (\mu_{t, i},\sigma_{t,i}^2))).
\end{split}
\end{equation}
where $\Phi = \{\mu_{t,i}, \sigma_{t, i}^2\}_{i=1:n}^{t=1:T}$ is a parameter of the GMM distribution, and $\kappa_t$ is the weight of $x_t$.

Based on the above assumptions and variational theory, to minimize $\mathcal{L}_g$ in Eq.\ref{eq14}, let the decoder output $\mathbf{\hat{x}}$ approximate the GMM distribution:
\begin{equation}\label{eq18}
\mathbf{\hat{x}} \sim p(\mathbf{\hat{x}}) \approx q_{\theta_d} (\mathbf{\hat{x}}|\mathbf{z}),		
\end{equation}
where $q_{\theta_d} (\mathbf{\hat{x}}|\mathbf{z})$ is the decoder output distribution, and it is a conditional probability distribution. Then, by letting $p(\hat{\mathbf{x}}) \approx q_{\theta _d}(\hat{\mathbf{x}}|\mathbf{z}) =p_{gmm}(\hat{\mathbf{x}};\Phi^*)$,  $\theta_d$ can be adjusted to obtain the optimal $\Phi^*$ to minimize the distribution distance between $p_{gmm}(\hat{\mathbf{x}};\Phi^*)$ and $p(\mathbf{x})$. 

Thus, $\mathcal{L}_g$ can be calculated as follows:
\begin{equation}\label{eq19}
\begin{split}
\mathcal{L}_g &= KLdiv(p(\mathbf{x}) \parallel p(\hat{\mathbf{x}})) 
\\
&\approx KLdiv(p(\mathbf{x}) \parallel q_{\theta _d}(\hat{\mathbf{x}}|\mathbf{z})) \\	 
&= - \int q_{\theta_d} (\hat{\mathbf{x}}|\mathbf{z}) \log \frac{q_{\theta_d}(\hat{\mathbf{x}}|\mathbf{z})} {p(\mathbf{x})} d\mathbf{x}  \\ 
&= \int q_{\theta_d} (\hat{\mathbf{x}}|\mathbf{z}) \log \frac {p(\mathbf{x})} {q_{\theta_d}(\hat{\mathbf{x}}|\mathbf{z})} d\mathbf{x}  \\ 
&= \mathbb{E}_{q_{\theta_d}(\hat{\mathbf{x}}|\mathbf{z})}
\left[\log \frac{p(\mathbf{x})} {q_{\theta_d} (\hat{\mathbf{x}}|\mathbf{z})}\right].
\end{split}
\end{equation}
Furthermore, in our system, given $\mathbf{x}$, $\mathcal{D}_\phi$ can identify that $\mathbf{x}$ is either $\mathbf{\hat{x}}$ or $\mathbf{x}$. If $\mathcal{D}_\phi(f_{\theta_e} (\mathbf{x})) = real$, then $p(\mathbf{x}) = p(\mathbf{x} | \mathcal{D}_\phi(f_{\theta_e} (\mathbf{x})) = real)$; if $\mathcal{D}_\phi(f_{\theta_e} (\mathbf{x})) = false$, then $q_{\theta_d} (\hat{\mathbf{x}} | z) = p(\mathbf{x} | \mathcal{D}_\phi(f_{\theta_e} (\mathbf{x})) = false)$. Thus, we have:
\begin{equation}\label{eq20}
\begin{split}
\frac{p(\mathbf{x})}{q_{\theta_d}(\hat{\mathbf{x}}|\mathbf{z})} &= \frac{p(\mathbf{x} | \mathcal{D}_\phi(f_{\theta_e}(\mathbf{x})) = real)} {p(\mathbf{x} | \mathcal{D}_\phi(f_{\theta_e}(\mathbf{x})) = false)} \\
&= \frac{p(\mathcal{D}_\phi(f_e(\mathbf{x})) = real | \mathbf{x})} {p(\mathcal{D}_\phi(f_{\theta_e}(\mathbf{x}))
= false | \mathbf{x})} \\
&= \frac{\mathcal{D}_\phi(f_{\theta_e} (\mathbf{x}))} {1 - \mathcal{D}_\phi(f_{\theta_e}(\mathbf{x}))}.
\end{split}
\end{equation}
In $\frac{p(\mathbf{x} | \mathcal{D}_\phi(f_{\theta_e}(\mathbf{x})) = real)} {p(\mathbf{x} | \mathcal{D}_\phi (f_{\theta_e} (\mathbf{x})) = false)} = \frac{ p(\mathcal{D}_\phi(f_{\theta_e} (\mathbf{x})) = real | \mathbf{x})} {p(\mathcal{D}_\phi (f_{\theta_e} (\mathbf{x})) = false | \mathbf{x})}$, Bayesian rules are used, and the edge probability is assumed to be equal, that is $p(\mathcal{D}_\phi (f_{\theta_e} (\mathbf{x})) = real) = p(\mathcal{D}_\phi (f_{\theta_e} (\mathbf{x})) = false)$. Therefore, according to the density ratio technique \cite{bib11}, we have:
\begin{equation}\label{eq21}
\begin{split}
\mathcal{L}_g&=KLdiv(p(\mathbf{x})\parallel p({\hat{\mathbf{x}}})) 
\approx E_{q_{\theta_d}(\hat{\mathbf{x}} |\mathbf{z})} [\log \frac{p(\mathbf{x})}{q_{\theta _d} (\hat{\mathbf{x}}|\mathbf{z}) }] 
\\
&= E_{q_{\theta_d}(\hat{\mathbf{x}}|\mathbf{z})} [\log \frac{\mathcal{D}_\phi(f_{\theta_e}(\mathbf{x}))} {1- \mathcal{D}_\phi (f_{\theta_e} (\mathbf{x}))}].\\
&= E_{q_{\theta_d}(\hat{\mathbf{x}}|\mathbf{z})} [\log \frac{\mathcal{D}_\phi(f_{\theta_e}(G_\varphi(\mathcal{X}))} {1- \mathcal{D}_\phi (f_{\theta_e} (G_\varphi(\mathcal{X})))}].
\end{split}
\end{equation}

\textbf{Identification loss} ($\mathcal{L}_\omega$) .		
For $\mathcal{L}_\omega$, similar to $\mathcal{D}_\phi$, $\mathcal{C}_\omega (h_T)$ can determine whether the potential variable $h_T$ is from $\mathbf{x}^S$ or $\mathbf{x}^T$. If $\mathcal{C}_\omega (h_T)=1$, then, $p(h_T|\mathbf{x}^S)=p(h_T | \mathcal{C}_\omega (h_T)=1)$; if $\mathcal{C}_\omega (h_T)=0$, then $p(h_T| \mathbf{x}^T)=p(h_T|\mathcal{C}_\omega(h_T)=0)$, which is similar to the expression in Eq. (\ref{eq20}). Thus, we have:
\begin{equation}\label{eq22}
	\frac{p(h_T|\mathbf{x}^S)}{p(h_T | \mathbf{x}^T)} = \frac{{{\mathcal{C}_\omega}(h_T)}}{{1 - {\mathcal{C}_\omega}(h_T)}}.
\end{equation}
Therefore, according to the density ratio technique \cite{bib11}, we have:
\begin{equation}\label{eq23}
	\begin{split}
		\mathcal{L}_\omega & \approx \mathbb{E}_{q_{\theta _e} ({h_T} | \mathbf{x})} [ \log \frac{p(h_T | \mathbf{x}^S)} 
		{p (h_T | \mathbf{x}^T)} ]  
		\\
		& =\mathbb{E}_{q_{\theta_e} (h_T | \mathbf{x})} [\log \frac{\mathcal{C}_\omega (h_T)} {1 - \mathcal{C}_\omega (h_T)}].
	\end{split}	
\end{equation}
Similar to $q_{\theta_d}$, which is related to the decoder, this paper also assumes the  $q_{\theta_e}$ obeys the GMM distribution, so it is also related to the encoder.
   
\textbf{Total loss} $\mathcal{L}_{total}$. The total loss function is defined as:
\begin{equation}\label{eq24}
{\mathcal{L}_{total}} = \lambda^{class} \mathcal{L}_{class} + \lambda^{\omega} \mathcal{L}_\omega +\lambda^{\phi} \mathcal{L}_g.
\end{equation}
Based on the above formula,  only the parameters ${\Theta=\{\varphi, \theta_e, \theta_c, \theta_d, \theta_{att}, \phi, \omega}\}$ need to be adjusted by using the gradient descent method, as follows:
\begin{equation}\label{eq25}
\Theta^{t+1} \leftarrow \Theta^t + \frac{\partial \mathcal{L}_{total}(\Theta)} {\partial \Theta}.
\end{equation}

During training, given the training data $\mathcal{X}$ and the ground truth label $y$, the encoder $f_{\theta_e}$ and classifier $f_{\theta_c}$ are first trained, and the parameters $\theta_e$ and $\theta_c$ are learned by the minimum loss function $\mathcal{L}(\hat{y}, y)$. Then, based on the obtained $\theta_e$ and feature $\mathbf{h}$, $\mathcal{C}_\omega$ and $\mathcal{D}_\phi$  two-class classifiers, the parameters $\omega$ and $\phi$ are trained by a standard random variable $z$ and the sequence feature $h_T$. Then, based on the random signal $\mathbf{z}$, $f_{\theta_d}$ generates $\mathbf{\hat{x}}$ with the minimum loss functions $\mathcal{L}_g$, and parameters $\theta_d$ and $\theta_{att}$ are updated. Finally, all parameters are adjusted by the minimum loss function $\mathcal{L}_{total}$.


\section{Experiments}\label{sec3}
\subsection{Dataset} 
\textbf{IIA}\cite{bib12}: The dataset contains 22 channels of EEG signals from 9 subjects (A01-A09). The EEG signal sampling rate is 250Hz. There are 4 class MI tasks, including left hand, right hand, tongue, and foot.  There are total 288 training samples, 72 samples for each class, and 288 testing samples, 72 samples for each class. A time period of $[2,6]$ seconds is selected for evaluation.

\textbf{IIB}\cite{bib13}: The IIB contains 3 channel EEG signals from 9 subjects (B01-B09). Subjects involved in left-handed and right-handed MI activities. the EEG signal sampling rate is 250 Hz. For each subject, five times of data collection were performed. The first three times were used for training and the rest for testing. There are 400 training samples and 320 test samples in total. The time period of $[3,7]$ seconds was used in the experiment.

\subsection{Baseline}

For evaluating performance of the proposed method, some state-of-art methods are compared, including  FBCSP \cite{bib14}\cite{bib15}, matrix EEG classifier SMM \cite{bib16}\cite{bib17}, SSMM \cite{bib18}, MI-CNN \cite{bib19}\cite{bib20}, ConvNet \cite{bib21}\cite{bib22}, and two domain adaptation methods, CCSP \cite{bib23} \cite{bib24}and SSCSP \cite{bib25}\cite{bib26}. In addition, the proposed method was also compared to the methods that achieve good results on the two datasets in recent years, including C2CM \cite{bib27}, Bo \cite{bib28}, HSS-ELM \cite{bib29}, and TLCSD \cite{bib30}.


\subsection{Experimental Setup}

All tests are conducted on the NVIDIA TitanX Pascal GPU. The cross-entropy loss function was minimized using random gradient descent and the Adam update rule \cite{bib31}. The learning rate was set to $10^{-4}$, and the retention probability of existing operations was 0.5. On the basis of
the EEG acquisition model, the two-dimensional data grid was transformed into a matrix size of $10 \times 11$. 

On these datasets, EEG channels were used for classification, and three electrooculogram (EOG) channels were directly discarded without any artifact removal operations. 2-second motor imagery waveform data between 0.5 and 2.5 seconds were used after the emergence of motor imagery cues. The motion imagination continuous EEG data were segmented within a given time range \cite{bib12}.

The same parameter setting was applied to all baseline methods. For the linear method, the band-pass filtering frequency range is 8-12 Hz, and it is called $\mu$ -band because it exhibits the strongest ERD during motion imaging. For nonlinear methods, the preprocessing steps and hyper-parametric estimation steps given in the corresponding literature were adopted, and these signals can produce the highest accuracy. For all nonlinear methods, the learning rate was set to $10^{-4}$.

\subsubsection{Performance Analysis}
In this section, the overall performance of the proposed model CGSDA is analyzed, and the comparison results of different models are discussed.

To study the influence of spatial-temporal information on EEG-MI recognition, various baseline models were established, including 1-DCNN, 2-DCNN, and 3D-CNN models, where the 2D-CNN and 3D-CNN models were constructed according to \cite{bib32}. The overall performance comparison results between the our model and other methods are presented in Table~\ref{tab1}. From results, it can be seen that our RNN-based identification and generation model achieve 81.23\% recognition accuracy, which is superior to many of the most advanced methods and baseline models. Meanwhile, 2D-CNN performed better than 1D-CNN, indicating that spatial+temporal representation provides more information to improve the performance of the EEG-MI classification model. Besides, the 3D-CNN can extract local spatiotemporal features with complex spatial dependence and time evolution information of adjacent nodes, so it achieved an accuracy improvement of about 4\% to 2D-CNN.

\begin{table}[h]
\caption{Performance Comparison of Different Cascade on IIA\cite{bib12}}\label{tab1}%
\begin{tabular}{@{}ll@{}}
\toprule
Cascade structure & Recognition Accuracy  \\
\midrule
2D-GCN+BiLSTM+FC+Softmax & 81.23 $\pm$ 1.23 \%   \\ 
2D-GCN+LSTM+FC+Softmax & 78.52 $\pm$ 2.33 \%   \\ 
2D-CNN+BiLSTM+FC+Softmax & 72.87 $\pm$ 1.87 \%   \\ 
2D-CNN+LSTM+FC+Softmax & 71.39 $\pm$ 2.85 \%   \\ 
1D-CNN+FC+Softmax & 65.55 $\pm$ 2.27 \%   \\ 
3D-CNN+FC+Softmax & 76.46 $\pm$ 3.19 \%   \\ 
\botrule
\end{tabular}
\end{table}

Inspired by 2D-CNN and 3D-CNN models, this paper proposed to organize the original data in a 2D graph structure to preserve the spatial information, and then, the proposed 2-DGCN uses the spatial topological relationship of nodes to express the local spatial features. Compared with 2D-CNN, our method utilizes the correlation between nodes and further exploits the exact attribution of EEG signals. All experiments were performed 5 times to reduce randomness and average accuracy was recorded. The test result (81.23\%) is even better than that of 3D-CNN (76.46\%). In contrast to previous studies, our model can directly use raw EEG data, select relevant frequency bands or perform complex preprocessing steps without prior knowledge, but there is a risk of losing key information or inducing a lot of noise. In fact, the GCN module used in the system can be regarded as a frequency filter of the original EEG data.  

Our model uses an RNN-based EEG generation and recognition framework. In the recognition stage, the cascade structure is “2D-GCN-BiLSTM+FC+Softmax”; in the generation stage, the cascade structure is “BiLSTM-Att-LSTM”, where “Att” is an attention module. In the process of EEG signal analysis, 2D-GCN-BiLSTM is used to extract global temporal features. The above analysis indicates that 2D-GCN has good performance to deeply mine the fine attributes of EEG at time $t$, and BiLSTM can fully utilize the context information. Therefore, the combined structure 2D-GCN-BiLSTM can maximize the acquisition of EEG-typical features. Meanwhile, the comparative results show that the combination of local spatial features and global temporal features can improve the analysis ability of EEG signals. As shown in Table~\ref{tab1}, the proposed recognition cascade structure ``2D-GCN-BiLSTM+FC+Softmax" obtained the highest accuracy (81.23\%), which is better than that of ``2D-CNN-BiLSTM+FC+Softmax" (72.87\%), indicating that the proposed GCN-based EEG presentation is superior to CNN-based presentation.




In addition, the influence of different system parameters on the model recognition results was also tested. These parameters include the number of layers of GCN, the number of hidden states of RNN, the regular transformation method, and the FC size. As shown in Table~\ref{tab2}, the effects of different GCN layers on the recognition results were compared. The results indicate that with the increase in the number of layers, the recognition accuracy was continuously improved. For example, the recognition accuracy was 73.92\% with 1-layer GCN, and 81.23\% with 3-layer, but the improvement of accuracy from 2-layer to 3-layer was limited, i.e., about 2\%. Considering the balance between the calculation cost and accuracy in practical applications, it is appropriate to select 3-layer GCN. Table~\ref{tab3} presents the recognition results based on different parameters, regularization methods, and different structures of the proposed model. It can be seen that the size of the hidden state of BiLSTM has a great influence on the performance of recognition. The model performs best when the hidden state was set to 128. Besides, the $L_2$ regularization method can improve the performance of the model, while the $L_1$ regularization method can significantly reduce the performance. Larger FC layers help to improve the model performance.The results in Table~\ref{tab3} indicate that the recognition accuracy is the highest when the FC size is 1024, Dropout+$L_2$ is used, and the hidden state is 128. 




\begin{table}[h]
    \caption{Performance comparison of different hyper-parameters on IIA\cite{bib12}}\label{tab3}%
    \begin{tabular}{@{}p{1.2cm}p{1.8cm}p{0.8cm}p{2cm}@{}}
    \toprule
    \parbox{1.2cm}{Hidden\\State} & \parbox{1.8cm}{Regularization\\Method} & FC Size & Accuracy  \\
    \midrule
    3&	Dropout+$L_2$&	1024	& 65.11 $\pm$ 0.79 \%  \\
    6&	Dropout+$L_2$&	1024	& 69.89 $\pm$ 1.23 \%  \\
    10&	Dropout+$L_2$&	1024	& 72.37 $\pm$ 2.18 \%  \\
    60&	Dropout+$L_2$&	1024	& 78.89 $\pm$ 2.33 \%  \\
    128&	Dropout+$L_2$&	1024	& 81.23 $\pm$ 1.23 \%  \\
    1024&	Dropout+$L_2$&	1024	& 75.49 $\pm$ 1.12 \%  \\
    128&	Dropout+$L_2$&	512	& 78.23 $\pm$ 1.11 \%  \\
    128&	Dropout+$L_2$&	256	& 77.99 $\pm$ 1.28 \%  \\
    128&	Dropout &	1024	& 73.59 $\pm$ 2.61 \%  \\
    256&	Dropout &	1024	& 73.78 $\pm$ 0.79 \%  \\
    128&	Dropout+$L_1$&	1024	& 76.21 $\pm$ 0.58 \%  \\
    \botrule
    \end{tabular}
\end{table}

In addition, the EEG data augment based on sequence generation can enhance the performance of the classifier. As shown in Table~\ref{tab4}, only the source-domain data $\mathcal{X}^S$ (288 samples, 72 samples per class, 288 samples were used for training, and other 288 samples were used for testing) was utilized for EEG recognition on IIA \cite{bib12}, and the average accuracy was 67.51\%. Only the generated data $\hat{\mathbf{x}}^g$ (288 generated samples were used for training, 288 samples in the test set were used for testing) were used to identify EEG signals, and the average accuracy reached 65.99\%. To combine $\mathcal{X}^S$ with $\mathbf{\hat{x}}^g$ (288 real samples [50\%], 288 generated samples[50\%]) for training, 288 test samples were used for the test, and the average accuracy reached 72.79\%. If more generated data (20\% $\mathcal{X}^S$, 80\% $\mathbf{\hat{x}}^g$) was used, the recognition performance will be further improved (75.25\%). To sum up, the proposed model can enhance recognition performance by data augmentation. Meanwhile, the distribution of the real data and the distribution of the generated data were compared. As shown in Figure~\ref{fig3}, two comparison examples between the real data and generated data are given, where the first one is class3 sequence data in A05. From the comparison results of ridge maps and encephalograms, it can be seen that the distribution of the real data and generated data is highly similar.  

\begin{table*}[h]
    \caption{Performance comparison of different hyper-parameters on IIA\cite{bib12}}\label{tab4}%
    \begin{tabular}{@{}lllll@{}}
    \toprule
    Subject& $\mathcal{X}^S$ & $\mathbf{\hat{x}}^g$ & $\mathcal{X}^S_{50 \%}$,$\mathbf{\hat{x}}^g_{50 \%}$ & $\mathcal{X}^S_{20 \%}$,$\mathbf{\hat{x}}^g_{80 \%}$ \\
    \midrule
    A01&75.35$\pm$1.23 \%& 74.99$\pm$1.56 \%& 78.23$\pm$2.59 \%& 80.56$\pm$2.26 \% \\
            
    A02&56.39$\pm$2.20 \%& 55.55$\pm$2.02 \%& 63.23$\pm$1.89 \%& 68.06$\pm$0.78 \% \\	
    
    A03&80.33$\pm$0.89 \%& 78.55$\pm$1.25 \%& 83.33$\pm$1.59 \%& 85.69$\pm$0.98 \% \\
        
    A04 &61.22$\pm$1.12 \%& 60.58$\pm$1.36 \%& 66.38$\pm$0.56 \%& 68.72$\pm$1.11 \% \\
        
    A05 &56.39$\pm$0.69 \%& 55.42$\pm$1.12 \%& 63.55$\pm$0.77 \%& 64.58$\pm$1.56 \% \\
            
    A06 &45.32$\pm$1.17 \%& 40.36$\pm$2.03 \%& 55.69$\pm$2.20 \%& 60.78$\pm$1.26 \% \\
    
    A07 & 81.11$\pm$1.11 \%& 79.59$\pm$0.56 \%& 86.12$\pm$2.21 \%& 86.99$\pm$2.28 \% \\
            
    A08 &80.25$\pm$1.02 \%& 79.33$\pm$2.21 \%& 83.22$\pm$1.36 \%& 83.65$\pm$2.25 \% \\
            
    A09 &71.23$\pm$1.89 \%& 69.55$\pm$0.39 \%& 75.33$\pm$1.68 \%& 78.23$\pm$2.25 \% \\

    Avg. &67.51$\pm$1.26 \%&65.99$\pm$1.39 \%&72.79$\pm$1.65 \%&75.25$\pm$1.64 \% \\
    \botrule
    \end{tabular}
\end{table*}



\begin{figure*}[h]
\centering
\includegraphics[width=0.9\textwidth]{Fig3.eps}
\caption{Comparison of the distribution of real EEG data and generated data. (a) The left column: the ridge map for the mean value of 22-channel real sequence data (A05, class3);  the channel is ``Fz, FC3,$\cdots$"; the right column: 6 encephalograms,  and each gram is related to the mean value of the clustered real sequence groups. (b) The left column: the ridge map for the mean value of 22-channel generated sequence data (A05, class3); the right column: 6 encephalograms, and each gram is related to the mean value of clustered generated sequence groups. (c) The left column: the ridge map for the mean value of 22-channel real sequence data (A05, class2); the right column: 6 encephalograms. (d) The left column: the ridge map for the mean value of 22-channel generated sequence data (A05, class2); the right column: 6 encephalograms.}\label{fig3}
\end{figure*}

\subsubsection{Comparative Analysis}
Since EEG data is non-stationary and varies with different subjects, it greatly affects the classification performance of traditional methods, the comparison results are shown in Figure~\ref{fig4}(a). The traditional methods fail to adaptively optimize the features for classification tasks, leading to poor classification accuracy. By contrast, our method can learn identifiable features and classifiers in the end-to-end model, and it utilizes information from other real subjects to improve classification performance through adversarial learning. 

\begin{figure*}[h]
    \centering
    \includegraphics[width=0.9\textwidth]{Fig4.eps}
    \caption{Classification accuracy of different algorithms. (a) Classification accuracy of different algorithms on IIA for
    different subjects; (b) Classification accuracy of different algorithms on IIB for different subjects.}\label{fig4}
\end{figure*}

Our method was further tested on the IIB dataset \cite{bib13}, and the test results are presented in Figure~\ref{fig4}(b). Compared with other methods, our method achieved the highest average accuracy among almost all subjects, which shows that the combination of GCN, sequence generation, and DA is effective for EEG classification in the end-to-end model. The DA realized by adversarial learning can extract the data distribution from the data samples of different subjects. 



\subsubsection{Ablation Study}
The experiments below were conducted to study different parameter settings, and the test results proved the importance of using DA adversarial ($D_\omega$) and generation adversarial  ($D_\phi$) in the proposed method. Each experiment was performed five times to reduce randomness, and the average accuracy was recorded.


\textbf{DA Adversarial} ($D_\omega$). The performance comparison with different weight settings is shown in Table~\ref{tab5}. The test on the IIA dataset \cite{bib12} shows that when $\lambda_\omega=0$, Our proposed method is an end-to-end LSTM sequence classifier that has no domain adaptation module and removes the domain discriminator $D_\omega$ from the system.

\begin{sidewaystable}
    \caption{EEG Classification Performance of Models Trained with Different Adversarial Loss Functions and Weights $\lambda_\omega$ on Dataset IIA  of BCI Competition IV with $\lambda^{class}=1$ and $\lambda^\phi=0$ \cite{bib12}.}\label{tab5}
    \begin{tabular*}{\textheight}{@{\extracolsep\fill}lccccccc}
    \toprule%
    & \multicolumn{1}{@{}c@{}}{$\mathcal{X}^S$} & \multicolumn{1}{@{}c@{}}{$\mathcal{X}^S+\mathcal{X}^T$} & \multicolumn{5}{@{}c@{}}{$\mathcal{X}^S+\mathcal{X}^T$}\\
    \cmidrule{2-2}\cmidrule{3-3}\cmidrule{4-8}%
    Subject & $\lambda^\phi=0$ & $\lambda^\phi=0.5$ & $\lambda^\phi=1$ & $\lambda^\phi=0.3$ & $\lambda^\phi=0.5$ & $\lambda^\phi=0.7$ & $\lambda^\phi=0.8$ \\
    \midrule
    A01 & 75.35$\pm$1.26\% & 73.56$\pm$2.23\% & 80.23$\pm$1.56\% & 81.33$\pm$2.26\% & 82.25$\pm$0.59\% & 76.56$\pm$1.57\% & 72.58$\pm$2.89\% \\
    A02 & 56.39$\pm$1.11\% & 55.36$\pm$2.02\% & 59.03$\pm$0.56\% & 61.22$\pm$1.89\% & 59.66$\pm$1.02\% & 46.56$\pm$0.89\% & 32.58$\pm$1.05\% \\
    A03 & 80.33$\pm$1.03\% & 81.22$\pm$1.11\% & 87.23$\pm$0.65\% & 88.33$\pm$2.21\% & 89.25$\pm$1.03\% & 88.56$\pm$0.32\% & 83.58$\pm$1.49\% \\
    A04 & 61.22$\pm$1.56\% & 55.19$\pm$0.98\% & 67.58$\pm$1.11\% & 71.75$\pm$2.02\% & 78.85$\pm$1.05\% & 72.36$\pm$2.23\% & 65.33$\pm$0.78\% \\
    A05 & 56.39$\pm$2.03\% & 49.66$\pm$1.98\% & 59.06$\pm$2.03\% & 66.58$\pm$3.21\% & 63.57$\pm$1.48\% & 65.29$\pm$2.03\% & 62.33$\pm$1.96\% \\
    A06 & 45.32$\pm$1.22\% & 51.22$\pm$0.56\% & 52.33$\pm$1.15\% & 53.26$\pm$1.75\% & 61.23$\pm$2.05\% & 52.01$\pm$3.26\% & 35.19$\pm$1.22\% \\
    A07 & 81.11$\pm$2.23\% & 65.28$\pm$1.88\% & 85.17$\pm$2.03\% & 86.22$\pm$1.15\% & 89.38$\pm$2.01\% & 79.99$\pm$1.23\% & 77.48$\pm$2.15\% \\
    A08 & 80.25$\pm$1.33\% & 74.15$\pm$1.03\% & 83.22$\pm$0.56\% & 85.77$\pm$1.77\% & 88.36$\pm$2.05\% & 83.33$\pm$2.03\% & 75.25$\pm$2.25\% \\
    A09 & 71.23$\pm$1.89\% & 73.25$\pm$0.56\% & 82.69$\pm$0.98\% & 83.15$\pm$0.78\% & 82.99$\pm$1.56\% & 81.25$\pm$1.23\% & 75.28$\pm$1.12\% \\
    Avg. & 67.51$\pm$1.51\% & 64.3 $\pm$1.37\% & 72.94$\pm$1.18\% & 75.29$\pm$1.89\% & 77.28$\pm$1.42\% & 63.26$\pm$1.64\% & 64.4$\pm$1.65\% \\
    \botrule
    \end{tabular*}
\end{sidewaystable}

Only the data from the source domain $\mathcal{X}^S$ was first selected to train the encoder $f_{\theta_e}$ and classifier $f_{\theta_c}$ based on labels. As shown in Table~\ref{tab5}, the overall recognition average accuracy of the $\mathcal{X}^S$-based model was 67.51\%, which is slightly lower than that of many traditional EEG classifiers, e.g., the average accuracy of FBCSP is 67.75\%. Meanwhile, for the $\mathcal{X}^S$-based model, the performance of subjects decreased significantly, indicating that DA adversarial learning is more important for subjects with a poor SNR.

It can be seen from Table~\ref{tab5} that the mean accuracy of our proposed method is competitive over other state-of-the-art methods, such as the model of adversarial learning (77.28\% [$\lambda^\omega=0.5$] and C2CM \cite{bib27}: 75.50\%). This shows that for the objects with EEG signals of a high SNR, the depth model proposed in this paper can obtain good results with a small amount of training data, but for objects with poor EEG data quality, a large amount of data is needed to train a reliable classifier.

To increase the training data, all the data of $\mathcal{X}^T$ and $\mathcal{X}^S$ were used to train a subject-independent classifier without considering adversarial learning, and the classification accuracy is reported as ``$\mathcal{X}^T+\mathcal{X}^S$" in Table~\ref{tab5}. Although the performance of A03 and A09 was significantly improved, the average accuracy decreased to 64.32\%. In addition, the target data of each subject were used to fine-tune the pre-trained depth model on the source data, and the results are presented in Table~\ref{tab5} ($\lambda^\omega =0.1, \cdots, 0.8$). The results show that the method based on a domain discriminator can reduce the distribution difference and improve the classification performance by using useful information in the source data.

\textbf{Generation adversarial} ($D_{\phi}$): the impact of generation adversarial loss with different weights was also studied on dataset IIA \cite{bib12}, and the results are presented in Table~\ref{tab6}. Here, two different models were considered: one is the model trained only based on generation adversarial learning ($\lambda^\phi \neq 0$, and $\lambda^\omega \neq 0$), and the other is the model trained based on DA adversarial learning and generation adversarial learning  ($\lambda^\phi \neq 0$, and $\lambda^\omega \neq 0$). When $\lambda^\omega=0$, $D_\omega$ is inactive. It is observed that when $\lambda^\phi=0, 0.5, 1$ in Table~\ref{tab6}, the classification accuracy will be improved, i.e., when $\lambda^\phi \leq 0$, and $\lambda^\omega \neq 0$, all adversarial learning is not used, and the basic recognition accuracy is 67.51\%; when $\lambda^\phi = 0.5, 1$, and $\lambda^\omega = 0$, only $D_\phi$ is used, and the average accuracy is 70.29\% and 70.82\%, respectively. Thus, generation adversarial is conducive to performance improvement.

\begin{sidewaystable}
    \caption{EEG Classification Performance of Models Trained with Different Adversarial Loss Functions and Weights $\lambda_\phi$ on Dataset IIA  of BCI Competition IV with $\lambda^{class}=1$ and $\lambda^\omega=0.5$. \cite{bib12}}\label{tab6}
    \begin{tabular*}{\textheight}{@{\extracolsep\fill}lccccccc}
    \toprule%
    & \multicolumn{3}{@{}c@{}}{$\lambda^\omega=0$} & \multicolumn{4}{@{}c@{}}{$\lambda_\omega=0.5$} \\
    \cmidrule{2-4}\cmidrule{5-8}%
    Subject & $\lambda^\phi=0$ & $\lambda^\phi=0.5$ & $\lambda^\phi=1$ & $\lambda^\phi=0.3$ & $\lambda^\phi=0.5$ & $\lambda^\phi=0.7$ & $\lambda^\phi=0.8$ \\
    \midrule
    A01 & 75.35$\pm$1.33\% & 78.33$\pm$1.56\% & 78.98$\pm$1.11\% & 80.56$\pm$2.08\% & 85.69$\pm$0.99\% & 76.56$\pm$1.33\% & 75.99$\pm$2.22\% \\
    A02 & 56.39$\pm$0.69\% & 59.63$\pm$2.11\% & 61.36$\pm$1.22\% & 68.06$\pm$1.11\% & 69.33$\pm$1.56\% & 59.56$\pm$0.33\% & 58.26$\pm$2.22\% \\
    A03 & 80.33$\pm$0.66\% & 81.21$\pm$1.23\% & 82.56$\pm$1.22\% & 85.69$\pm$1.55\% & 91.23$\pm$1.66\% & 83.25$\pm$0.98\% & 83.58$\pm$2.26\% \\
    A04 & 61.22$\pm$0.87\% & 63.19$\pm$0.79\% & 67.58$\pm$1.56\% & 68.72$\pm$1.49\% & 75.61$\pm$1.46\% & 69.66$\pm$2.02\% & 65.33$\pm$2.01\% \\
    A05 & 56.39$\pm$1.99\% & 61.22$\pm$1.92\% & 63.06$\pm$1.56\% & 64.58$\pm$1.11\% & 79.65$\pm$1.36\% & 61.87$\pm$1.35\% & 60.68$\pm$0.75\% \\
    A06 & 45.32$\pm$1.88\% & 52.33$\pm$1.99\% & 56.21$\pm$1.78\% & 60.78$\pm$1.65\% & 72.22$\pm$2.22\% & 52.01$\pm$2.03\% & 52.99$\pm$2.06\% \\
    A07 & 81.11$\pm$2.11\% & 82.28$\pm$2.26\% & 85.17$\pm$2.15\% & 86.99$\pm$1.56\% & 71.23$\pm$1.48\% & 83.99$\pm$1.59\% & 83.48$\pm$0.99\% \\
    A08 & 80.25$\pm$0.99\% & 81.15$\pm$0.65\% & 83.22$\pm$1.23\% & 83.65$\pm$1.56\% & 96.87$\pm$1.48\% & 82.36$\pm$1.88\% & 81.36$\pm$1.99\% \\
    A09 & 71.23$\pm$1.22\% & 73.25$\pm$1.22\% & 77.25$\pm$1.23\% & 78.23$\pm$1.48\% & 89.26$\pm$0.98\% & 81.56$\pm$0.68\% & 76.55$\pm$2.24\% \\
    Avg. & 67.51$\pm$1.31\% & 70.29$\pm$1.52\% & 70.82$\pm$1.45\% & 75.25$\pm$1.51\% & 81.23$\pm$1.46\% & 72.31$\pm$1.35\% & 70.91$\pm$1.86\% \\
    \botrule
    \end{tabular*}
\end{sidewaystable}

Meanwhile, when $\lambda^\omega \neq 0, \lambda^\phi \neq 0$, $D_\omega$ and $D_\phi$ are used. In Table~\ref{tab5}, when $\lambda^\omega = 0.5$, only using DA can obtain an average recognition accuracy of 77.28\% , so the best DA-based recognition results were selected as the baseline. When $\lambda^\omega = 0.5$, in Table~\ref{tab6}, $\lambda^\phi=0.3,0.5,0.7,0.8$, it can be seen that all average accuracy is superior to that of only using $D_\phi$; especially, for $\lambda^\phi=0.5$ and $\lambda^\omega = 0.5$, the classification accuracy was improved from 67.51\% to 81.23\%. For subjects with low signal quality, this improvement is more significant. For example, the accuracy of A02 was improved by 13\%. This result shows that $D_\phi$ can process intra-group changes. As a result, the learned features are more discriminating. Actually, similar phenomena have occurred in other subjects. The study also shows that if there is no $D_\phi$, the features are more dispersed, and the entropy is larger. 

\section{Conclusion}\label{sec4}

As we known, EEG-based classification is very important for BCI control. In this paper, we propose an EEG generative and recognition model called the CSGDA model. The model can greatly enhances the EEG identification performance of the system, at the same time, a novel RNN-based sequence generation is presented, which can be applied to many fields related to sequence signal processing, such as music generation, text generation, chat robot, etc. Our contributions includes:

(1) A graph-based EEG signal representation is proposed, which can reflect the temporal and spatial attribution of EEG signals. The test results show that the cascade structure "2D-GCN" is superior to "2D-CNN", "1-D-CNN" and "3D-CNN", and "2D-GCN+BiLSTM" can obtain the best recognition accuracy. 

(2) We combine cGAN-based sequence generation and DA method to extract across-subject sequence feature. Trough adversarial learning, the extracted feature is strong discriminable. Validation experiments were conducted on a large-scale open-source EEG dataset, and the proposed model achieved a classification accuracy of 81.23\%, which is better than that of the most advanced methods.

In the future work, we hope to apply EEG recognition and generation for actual control systems, such as the control of wheelchairs. On the other hand, we also try to embed algorithms into mobile devices to control multiple daily devices.

\backmatter

\bmhead{Supplementary information}

If your article has accompanying supplementary file/s please state so here. 

Authors reporting data from electrophoretic gels and blots should supply the full unprocessed scans for key as part of their Supplementary information. This may be requested by the editorial team/s if it is missing.

Please refer to Journal-level guidance for any specific requirements.

\bmhead{Acknowledgements}

This research was partially funded by the National Natural Science Foundation of China (Grants 62071366 and 61671362), the Natural Science Foundation of Shaanxi Province (Grant 2025JC-YBMS-702), and the Excellent Doctoral Dissertation Cultivation Fund of Xi'an Technological University (Project YB202506).

\section*{Declarations}
\begin{itemize}
\item Funding: This research was partially funded by the National Natural Science Foundation of China (Grants 62071366 and 61671362), the Natural Science Foundation of Shaanxi Province (Grant 2025JC-YBMS-702), and the Excellent Doctoral Dissertation Cultivation Fund of Xi'an Technological University (Project YB202506).
\item Competing interests All authors certify that they have no affiliations with or involvement in any organization or entity with any financial interest or non-financial interest in the subject matter or materials discussed in this manuscript.
\item Ethics approval and consent to participate: Not applicable
\item Consent for publication: All authors have read and approved the final manuscript and consent to its publication in Applied Intelligence.
\item Data availability:  Data available on request from the authors. The data that support the findings of this study are available from the corresponding author,Qk X, upon reasonable request.
\item Materials availability: This study did not use any unique physical materials. All analyses were conducted on publicly available or request-based EEG datasets and computational resources.
\item Code availability: Code available on request from the authors. The Code that support the findings of this study are available from the corresponding author,Qk X, upon reasonable request. 
\item Author contribution: All authors contributed to the study conception and design. Material preparation, data collection and analysis were performed by Peiran Liu, and Qinkun Xiao. The first draft of the manuscript was written by Peiran Liu and all authors commented on previous versions of the manuscript. All authors read and approved the final manuscript.
\end{itemize}

\bibliography{sn-bibliography}% common bib file
%% if required, the content of .bbl file can be included here once bbl is generated
%%\input sn-article.bbl

\end{document}
