This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024) (preloaded format=pdflatex 2024.8.28)  18 SEP 2025 10:55
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**"d:/phd/01EEG论文/Arabian Journal for Science and Engineering/sn-article-template/sn-article.tex"
(d:/phd/01EEG论文/Arabian Journal for Science and Engineering/sn-article-template/sn-article.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(./sn-jnl.cls
Document Class: sn-jnl 2019/11/18 v0.1: An authoring template for Springer Journal articles
(d:/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(d:/texlive/2024/texmf-dist/tex/latex/base/fleqn.clo
File: fleqn.clo 2016/12/29 v1.2b Standard LaTeX option (flush left equations)
\mathindent=\skip48
Applying: [2015/01/01] Make \[ robust on input line 50.
LaTeX Info: Redefining \[ on input line 51.
Already applied: [0000/00/00] Make \[ robust on input line 62.
Applying: [2015/01/01] Make \] robust on input line 74.
LaTeX Info: Redefining \] on input line 75.
Already applied: [0000/00/00] Make \] robust on input line 83.
) (d:/texlive/2024/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count188
\c@section=\count189
\c@subsection=\count190
\c@subsubsection=\count191
\c@paragraph=\count192
\c@subparagraph=\count193
\c@figure=\count194
\c@table=\count195
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen140
)
LaTeX Info: Redefining \rm on input line 146.
LaTeX Info: Redefining \sf on input line 147.
LaTeX Info: Redefining \tt on input line 148.
LaTeX Info: Redefining \bf on input line 149.
LaTeX Info: Redefining \it on input line 150.
LaTeX Info: Redefining \sl on input line 151.
LaTeX Info: Redefining \sc on input line 152.
LaTeX Info: Redefining \cal on input line 153.
LaTeX Info: Redefining \mit on input line 154.
LaTeX Info: Redefining \textsubscript on input line 212.
\columnhsize=\skip51
 (d:/texlive/2024/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (d:/texlive/2024/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (d:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count196
\Gm@cntv=\count197
\c@Gm@tempcnt=\count198
\Gm@bindingoffset=\dimen141
\Gm@wd@mp=\dimen142
\Gm@odd@mp=\dimen143
\Gm@even@mp=\dimen144
\Gm@layoutwidth=\dimen145
\Gm@layoutheight=\dimen146
\Gm@layouthoffset=\dimen147
\Gm@layoutvoffset=\dimen148
\Gm@dimlist=\toks18
)
\artcatbox=\box51
\aucount=\count199
\corraucount=\count266
\punctcount=\count267
\emailcnt=\count268
\c@affn=\count269
\addcount=\count270
\PacsCount=\count271
\PacsTmpCnt=\count272
\FMremarkdim=\dimen149
\fmremarkbox=\box52
 (d:/texlive/2024/texmf-dist/tex/latex/sttools/cuted.sty
Package: cuted 2021/10/04 v2.0 Mixing onecolumn and twocolumn modes
\At@ViperColsBreak=\toks19
\preCutedStrip=\toks20
\postCutedStrip=\toks21
\cuted@@tempbox@a=\box53
\cuted@@tempbox@c=\box54
\cuted@@tempbox@var=\box55
\hold@viper=\box56
\@viper=\box57
\cuted@@varbox@a=\box58
\cuted@@varbox@c=\box59
\cuted@@tempdim@spread=\dimen150
\cuted@@tempdim@a=\dimen151
\cuted@@tempdim@b=\dimen152
\ht@hold@viper=\dimen153
\ht@viper=\dimen154
\var@@pagediscards@ht=\dimen155
\stripsep=\skip52
\viper@penalty=\count273
)
\firstpagehtcheck=\dimen156
\labelwidthi=\dimen157
\labelwidthii=\dimen158
\labelwidthiii=\dimen159
\labelwidthiv=\dimen160
\figwidth=\dimen161
\figheight=\dimen162
\sidecapwidth=\dimen163
\wrapcapline=\dimen164
\totalwrapline=\dimen165
\wraptotline=\dimen166
\figurebox=\box60
\wrapfigcapbox=\box61
\figcapbox=\box62
\capbox=\box63
\headwidthskip=\skip53
\tabcapbox=\box64
\temptbox=\box65
\tempdime=\dimen167
\tabhtdime=\dimen168
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/rotating.sty
Package: rotating 2016/08/11 v2.16d rotated objects in LaTeX
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (d:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (d:/texlive/2024/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen169
\Gin@req@width=\dimen170
) (d:/texlive/2024/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
\c@r@tfl@t=\count274
\rotFPtop=\skip54
\rotFPbot=\skip55
\rot@float@box=\box66
\rot@mess@toks=\toks22
) (d:/texlive/2024/texmf-dist/tex/latex/threeparttable/threeparttable.sty
Package: threeparttable 2003/06/13  v 3.0
\@tempboxb=\box67
) (d:/texlive/2024/texmf-dist/tex/latex/appendix/appendix.sty
Package: appendix 2020/02/08 v1.2c extra appendix facilities
\c@@pps=\count275
\c@@ppsavesec=\count276
\c@@ppsaveapp=\count277
) (d:/texlive/2024/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-01-20 v7.01h Hypertext links for LaTeX
 (d:/texlive/2024/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)) (d:/texlive/2024/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (d:/texlive/2024/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (d:/texlive/2024/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (d:/texlive/2024/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (d:/texlive/2024/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count278
) (d:/texlive/2024/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count279
)
\@linkdim=\dimen171
\Hy@linkcounter=\count280
\Hy@pagecounter=\count281
 (d:/texlive/2024/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-01-20 v7.01h Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (d:/texlive/2024/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count282
 (d:/texlive/2024/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-01-20 v7.01h Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4179.
Package hyperref Info: Link nesting OFF on input line 4184.
Package hyperref Info: Hyper index ON on input line 4187.
Package hyperref Info: Plain pages OFF on input line 4194.
Package hyperref Info: Backreferencing OFF on input line 4199.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4446.
\c@Hy@tempcnt=\count283
 (d:/texlive/2024/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip16
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4784.
\XeTeXLinkMargin=\dimen172
 (d:/texlive/2024/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count284
\Field@Width=\dimen173
\Fld@charsize=\dimen174
Package hyperref Info: Hyper figures OFF on input line 6063.
Package hyperref Info: Link nesting OFF on input line 6068.
Package hyperref Info: Hyper index ON on input line 6071.
Package hyperref Info: backreferencing OFF on input line 6078.
Package hyperref Info: Link coloring OFF on input line 6083.
Package hyperref Info: Link coloring with OCG OFF on input line 6088.
Package hyperref Info: PDF/A mode OFF on input line 6093.
 (d:/texlive/2024/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count285
\c@Item=\count286
\c@Hfootnote=\count287
)
Package hyperref Info: Driver (autodetected): hpdftex.
 (d:/texlive/2024/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2024-01-20 v7.01h Hyperref driver for pdfTeX
 (d:/texlive/2024/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
)
\Fld@listcount=\count288
\c@bookmark@seq@number=\count289
 (d:/texlive/2024/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip56
)
Package hyperref Info: Option `colorlinks' set `true' on input line 1471.
Package hyperref Info: Option `breaklinks' set `true' on input line 1471.
Package hyperref Info: Option `plainpages' set `false' on input line 1471.
Package hyperref Info: Option `bookmarksopen' set `true' on input line 1471.
Package hyperref Info: Option `bookmarksnumbered' set `false' on input line 1471.
 (d:/texlive/2024/texmf-dist/tex/latex/wrapfig/wrapfig.sty
\wrapoverhang=\dimen175
\WF@size=\dimen176
\c@WF@wrappedlines=\count290
\WF@box=\box68
\WF@everypar=\toks23
Package: wrapfig 2003/01/31  v 3.6
)
\wraplines=\count291
\@authorfigbox=\box69
\@authorfigboxdim=\skip57
\biofigadjskip=\skip58
 (d:/texlive/2024/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks24
\thm@bodyfont=\toks25
\thm@headfont=\toks26
\thm@notefont=\toks27
\thm@headpunct=\toks28
\thm@preskip=\skip59
\thm@postskip=\skip60
\thm@headsep=\skip61
\dth@everypar=\toks29
) (d:/texlive/2024/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
 (d:/texlive/2024/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
)) (d:/texlive/2024/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip62
\bibsep=\skip63
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count292
)) (d:/texlive/2024/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2021/03/15 v2.8 Span multiple rows of a table
\multirow@colwidth=\skip64
\multirow@cntb=\count293
\multirow@dima=\skip65
\bigstrutjot=\dimen177
) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip66

For additional information on amsmath, use the `?' option.
(d:/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks30
\ex@=\dimen178
)) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen179
) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count294
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count295
\leftroot@=\count296
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count297
\DOTSCASE@=\count298
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box70
\strutbox@=\box71
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen180
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count299
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count300
\dotsspace@=\muskip17
\c@parentequation=\count301
\dspbrk@lvl=\count302
\tag@help=\toks31
\row@=\count303
\column@=\count304
\maxfields@=\count305
\andhelp@=\toks32
\eqnshift@=\dimen181
\alignsep@=\dimen182
\tagshift@=\dimen183
\tagwidth@=\dimen184
\totwidth@=\dimen185
\lineht@=\dimen186
\@envbody=\toks33
\multlinegap=\skip67
\multlinetaggap=\skip68
\mathdisplay@stack=\toks34
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (d:/texlive/2024/texmf-dist/tex/latex/jknapltx/mathrsfs.sty
Package: mathrsfs 1996/01/01 Math RSFS package v1.0 (jk)
\symrsfs=\mathgroup6
) (d:/texlive/2024/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)
 (d:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
) (d:/texlive/2024/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
) (d:/texlive/2024/texmf-dist/tex/latex/ncctools/manyfoot.sty
Package: manyfoot 2019/08/03 v1.11 Many Footnote Levels Package (NCC)
 (d:/texlive/2024/texmf-dist/tex/latex/ncctools/nccfoots.sty
Package: nccfoots 2005/02/03 v1.2 NCC Footnotes Package (NCC)
)
\MFL@columnwidth=\dimen187
) (d:/texlive/2024/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen188
\lightrulewidth=\dimen189
\cmidrulewidth=\dimen190
\belowrulesep=\dimen191
\belowbottomsep=\dimen192
\aboverulesep=\dimen193
\abovetopsep=\dimen194
\cmidrulesep=\dimen195
\cmidrulekern=\dimen196
\defaultaddspace=\dimen197
\@cmidla=\count306
\@cmidlb=\count307
\@aboverulesep=\dimen198
\@belowrulesep=\dimen199
\@thisruleclass=\count308
\@lastruleclass=\count309
\@thisrulewidth=\dimen256
) (d:/texlive/2024/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (d:/texlive/2024/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count310
\float@exts=\toks35
\float@box=\box72
\@float@everytoks=\toks36
\@floatcapt=\box73
)
\@float@every@algorithm=\toks37
\c@algorithm=\count311
) (d:/texlive/2024/texmf-dist/tex/latex/algorithmicx/algorithmicx.sty
Package: algorithmicx 2005/04/27 v1.2 Algorithmicx

Document Style algorithmicx 1.2 - a greatly improved `algorithmic' style
\c@ALG@line=\count312
\c@ALG@rem=\count313
\c@ALG@nested=\count314
\ALG@tlm=\skip69
\ALG@thistlm=\skip70
\c@ALG@Lnr=\count315
\c@ALG@blocknr=\count316
\c@ALG@storecount=\count317
\c@ALG@tmpcounter=\count318
\ALG@tmplength=\skip71
) (d:/texlive/2024/texmf-dist/tex/latex/algorithmicx/algpseudocode.sty
Package: algpseudocode 

Document Style - pseudocode environments for use with the `algorithmicx' style
) (d:/texlive/2024/texmf-dist/tex/latex/listings/listings.sty
\lst@mode=\count319
\lst@gtempboxa=\box74
\lst@token=\toks38
\lst@length=\count320
\lst@currlwidth=\dimen257
\lst@column=\count321
\lst@pos=\count322
\lst@lostspace=\dimen258
\lst@width=\dimen259
\lst@newlines=\count323
\lst@lineno=\count324
\lst@maxwidth=\dimen260
 (d:/texlive/2024/texmf-dist/tex/latex/listings/lstpatch.sty
File: lstpatch.sty 2024/02/21 1.10 (Carsten Heinz)
) (d:/texlive/2024/texmf-dist/tex/latex/listings/lstmisc.sty
File: lstmisc.sty 2024/02/21 1.10 (Carsten Heinz)
\c@lstnumber=\count325
\lst@skipnumbers=\count326
\lst@framebox=\box75
) (d:/texlive/2024/texmf-dist/tex/latex/listings/listings.cfg
File: listings.cfg 2024/02/21 1.10 listings configuration
))
Package: listings 2024/02/21 1.10 (Carsten Heinz)
\c@theorem=\count327
\c@example=\count328
\c@remark=\count329
\c@definition=\count330
 (d:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-02-20 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count331
\l__pdf_internal_box=\box76
) (./sn-article.aux)
\openout1 = `sn-article.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 91.
LaTeX Font Info:    ... okay on input line 91.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 91.
LaTeX Font Info:    ... okay on input line 91.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 91.
LaTeX Font Info:    ... okay on input line 91.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 91.
LaTeX Font Info:    ... okay on input line 91.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 91.
LaTeX Font Info:    ... okay on input line 91.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 91.
LaTeX Font Info:    ... okay on input line 91.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 91.
LaTeX Font Info:    ... okay on input line 91.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 91.
LaTeX Font Info:    ... okay on input line 91.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 91.
LaTeX Font Info:    ... okay on input line 91.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: custom
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* bindingoffset: 17.07164pt
* modes: twoside 
* h-part:(L,W,R)=(50.07684pt, 455.24408pt, 75.11531pt)
* v-part:(T,H,B)=(73.97716pt, 614.57951pt, 156.49017pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24408pt
* \textheight=614.57951pt
* \oddsidemargin=-5.1215pt
* \evensidemargin=2.84532pt
* \topmargin=-24.94597pt
* \headheight=12.0pt
* \headsep=14.65314pt
* \topskip=10.0pt
* \footskip=28.82265pt
* \marginparwidth=34.1433pt
* \marginparsep=14.22636pt
* \columnsep=22.76219pt
* \skip\footins=18.0pt plus 6.0pt minus 3.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumntrue
* \@twosidetrue
* \@mparswitchtrue
* \@reversemargintrue
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(d:/texlive/2024/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count332
\scratchdimen=\dimen261
\scratchbox=\box77
\nofMPsegments=\count333
\nofMParguments=\count334
\everyMPshowfont=\toks39
\MPscratchCnt=\count335
\MPscratchDim=\dimen262
\MPnumerator=\count336
\makeMPintoPDFobject=\count337
\everyMPtoPDFconversion=\toks40
) (d:/texlive/2024/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (d:/texlive/2024/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
LaTeX Info: Redefining \S on input line 91.
LaTeX Font Info:    Redeclaring symbol font `AMSa' on input line 91.
LaTeX Font Info:    Overwriting symbol font `AMSa' in version `normal'
(Font)                  U/msa/m/n --> U/msa/m/n on input line 91.
LaTeX Font Info:    Overwriting symbol font `AMSa' in version `bold'
(Font)                  U/msa/m/n --> U/msa/m/n on input line 91.
Package hyperref Info: Link coloring ON on input line 91.
 (./sn-article.out) (./sn-article.out)
\@outlinefile=\write3
\openout3 = `sn-article.out'.

\c@lstlisting=\count338
LaTeX Font Info:    Calculating math sizes for size <12.045> on input line 124.
LaTeX Font Info:    Trying to load font information for U+msa on input line 124.
 (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 124.
 (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Trying to load font information for U+rsfs on input line 124.
 (d:/texlive/2024/texmf-dist/tex/latex/jknapltx/ursfs.fd
File: ursfs.fd 1998/03/24 rsfs font definition file (jk)
)

LaTeX Font Warning: Font shape `U/rsfs/m/n' in size <8.43146> not available
(Font)              size <8> substituted on input line 124.

LaTeX Font Info:    Calculating math sizes for size <11.04124> on input line 124.

LaTeX Font Warning: Font shape `U/rsfs/m/n' in size <5.52061> not available
(Font)              size <6> substituted on input line 124.

LaTeX Font Info:    Calculating math sizes for size <10.03749> on input line 124.

Underfull \hbox (badness 3895) in paragraph at lines 127--128
\OT1/cmr/m/n/10 due to their non-invasive nature and abil-
 []


Underfull \vbox (badness 10000) has occurred while \output is active []


Underfull \hbox (badness 10000) has occurred while \output is active
[]| []
 []

[1{d:/texlive/2024/texmf-var/fonts/map/pdftex/updmap/pdftex.map}


]
Underfull \hbox (badness 1314) in paragraph at lines 131--132
\OT1/cmr/m/n/10 cGAN-based sequence gen-er-a-tion and domain
 []


Underfull \hbox (badness 2564) in paragraph at lines 131--132
\OT1/cmr/m/n/10 ages domain adap-ta-tion (DA) tech-niques to
 []

Package epstopdf Info: Source file: <Fig1.eps>
(epstopdf)                    date: 2024-12-19 16:47:34
(epstopdf)                    size: 12670644 bytes
(epstopdf)             Output file: <Fig1-eps-converted-to.pdf>
(epstopdf)                    date: 2025-09-17 22:50:14
(epstopdf)                    size: 466277 bytes
(epstopdf)             Command: <repstopdf --outfile=Fig1-eps-converted-to.pdf Fig1.eps>
(epstopdf)             \includegraphics on input line 145.
Package epstopdf Info: Output file is already uptodate.
<Fig1-eps-converted-to.pdf, id=103, 799.98875pt x 977.6525pt>
File: Fig1-eps-converted-to.pdf Graphic file (type pdf)
<use Fig1-eps-converted-to.pdf>
Package pdftex.def Info: Fig1-eps-converted-to.pdf  used on input line 145.
(pdftex.def)             Requested size: 409.71689pt x 500.71451pt.

LaTeX Warning: `h' float specifier changed to `ht'.


Underfull \vbox (badness 10000) has occurred while \output is active []


Underfull \hbox (badness 10000) has occurred while \output is active
[]| []
 []

[2]
Underfull \vbox (badness 10000) has occurred while \output is active []


Underfull \hbox (badness 10000) has occurred while \output is active
[]| []
 []

[3
pdfTeX warning (ext4): destination with the same identifier (name{figure.1}) has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.165 \begin{equation}
                       <./Fig1-eps-converted-to.pdf>]
Package epstopdf Info: Source file: <Fig2.eps>
(epstopdf)                    date: 2024-12-19 17:00:30
(epstopdf)                    size: 3668532 bytes
(epstopdf)             Output file: <Fig2-eps-converted-to.pdf>
(epstopdf)                    date: 2025-09-17 22:50:16
(epstopdf)                    size: 547746 bytes
(epstopdf)             Command: <repstopdf --outfile=Fig2-eps-converted-to.pdf Fig2.eps>
(epstopdf)             \includegraphics on input line 172.
Package epstopdf Info: Output file is already uptodate.
<Fig2-eps-converted-to.pdf, id=247, 474.77374pt x 743.77875pt>
File: Fig2-eps-converted-to.pdf Graphic file (type pdf)
<use Fig2-eps-converted-to.pdf>
Package pdftex.def Info: Fig2-eps-converted-to.pdf  used on input line 172.
(pdftex.def)             Requested size: 216.24094pt x 338.76033pt.

Underfull \hbox (badness 1259) in paragraph at lines 176--177
\OT1/cmr/m/n/10 sequence $\OT1/cmr/bx/n/10.03749 x \OT1/cmr/m/n/10 = (\OML/cmm/m/it/10.03749 x[]; []  ; x[]\OT1/cmr/m/n/10 )$ into an encoded
 []


Underfull \hbox (badness 2237) in paragraph at lines 194--196
\OT1/cmr/m/n/10 vari-able $\OML/cmm/m/it/10.03749 h[]$ \OT1/cmr/m/n/10 in the decoder with the hid-
 []


Underfull \vbox (badness 10000) has occurred while \output is active []


Underfull \hbox (badness 10000) has occurred while \output is active
[]| []
 []

[4
pdfTeX warning (ext4): destination with the same identifier (name{figure.2}) has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.209 T
       his paper uses a single-layer LSTM network as the decoder. An LSTM-ba... <./Fig2-eps-converted-to.pdf>]
Overfull \hbox (5.90356pt too wide) detected at line 253
[]
 []


Underfull \vbox (badness 10000) has occurred while \output is active []


Underfull \hbox (badness 10000) has occurred while \output is active
[]| []
 []

[5]
Underfull \hbox (badness 1226) in paragraph at lines 302--304
\OML/cmm/m/it/10.03749 real$\OT1/cmr/m/n/10 , then $\OML/cmm/m/it/10.03749 p\OT1/cmr/m/n/10 (\OT1/cmr/bx/n/10.03749 x\OT1/cmr/m/n/10 ) = \OML/cmm/m/it/10.03749 p\OT1/cmr/m/n/10 (\OT1/cmr/bx/n/10.03749 x\OMS/cmsy/m/n/10.03749 jD[]\OT1/cmr/m/n/10 (\OML/cmm/m/it/10.03749 f[]\OT1/cmr/m/n/10 (\OT1/cmr/bx/n/10.03749 x\OT1/cmr/m/n/10 )) = \OML/cmm/m/it/10.03749 real\OT1/cmr/m/n/10 )$;
 []


Underfull \hbox (badness 4556) in paragraph at lines 302--304
\OT1/cmr/m/n/10 if $\OMS/cmsy/m/n/10.03749 D[]\OT1/cmr/m/n/10 (\OML/cmm/m/it/10.03749 f[]\OT1/cmr/m/n/10 (\OT1/cmr/bx/n/10.03749 x\OT1/cmr/m/n/10 )) = \OML/cmm/m/it/10.03749 false$\OT1/cmr/m/n/10 , then $\OML/cmm/m/it/10.03749 q[]\OT1/cmr/m/n/10 ([]\OMS/cmsy/m/n/10.03749 j\OML/cmm/m/it/10.03749 z\OT1/cmr/m/n/10 ) =
 []


Underfull \hbox (badness 1242) in paragraph at lines 311--313
\OT1/cmr/m/n/10 In $[] = []$,
 []


Underfull \hbox (badness 1584) in paragraph at lines 311--313
\OML/cmm/m/it/10.03749 real\OT1/cmr/m/n/10 ) = \OML/cmm/m/it/10.03749 p\OT1/cmr/m/n/10 (\OMS/cmsy/m/n/10.03749 D[]\OT1/cmr/m/n/10 (\OML/cmm/m/it/10.03749 f[]\OT1/cmr/m/n/10 (\OT1/cmr/bx/n/10.03749 x\OT1/cmr/m/n/10 )) = \OML/cmm/m/it/10.03749 false\OT1/cmr/m/n/10 )$. There-fore,
 []


Overfull \hbox (2.8343pt too wide) detected at line 321
[]
 []


Underfull \vbox (badness 10000) has occurred while \output is active []


Underfull \hbox (badness 10000) has occurred while \output is active
[]| []
 []

[6]
Underfull \hbox (badness 2726) in paragraph at lines 359--360
\OT1/cmr/m/n/10 For eval-u-at-ing per-for-mance of the pro-posed
 []


Underfull \hbox (badness 1077) in paragraph at lines 369--370
\OT1/cmr/m/n/10 and it is called $\OML/cmm/m/it/10.03749 ^^V$ \OT1/cmr/m/n/10 -band because it exhibits
 []


Underfull \hbox (badness 1917) in paragraph at lines 374--375
\OT1/cmr/m/n/10 CNN and 3D-CNN mod-els were con-structed
 []

LaTeX Font Info:    Calculating math sizes for size <8.03> on input line 378.

LaTeX Font Warning: Font shape `U/rsfs/m/n' in size <4.015> not available
(Font)              size <5> substituted on input line 378.


Overfull \hbox (4.92929pt too wide) in paragraph at lines 376--390
 [] 
 []


LaTeX Warning: `h' float specifier changed to `ht'.


Underfull \vbox (badness 10000) has occurred while \output is active []


Underfull \hbox (badness 10000) has occurred while \output is active
[]| []
 []

[7]
Underfull \hbox (badness 2080) in paragraph at lines 394--395
\OT1/cmr/m/n/10 of EEG at time $\OML/cmm/m/it/10.03749 t$\OT1/cmr/m/n/10 , and BiL-STM can fully
 []


LaTeX Warning: `h' float specifier changed to `ht'.


Overfull \hbox (14.44727pt too wide) in paragraph at lines 415--434
 [] 
 []


LaTeX Warning: `h' float specifier changed to `ht'.


Underfull \vbox (badness 10000) has occurred while \output is active []


Underfull \hbox (badness 10000) has occurred while \output is active
[]| []
 []

[8
pdfTeX warning (ext4): destination with the same identifier (name{table.1}) has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.437 
      ]

LaTeX Warning: `h' float specifier changed to `ht'.

Package epstopdf Info: Source file: <Fig3.eps>
(epstopdf)                    date: 2024-12-19 18:14:38
(epstopdf)                    size: 3810271 bytes
(epstopdf)             Output file: <Fig3-eps-converted-to.pdf>
(epstopdf)                    date: 2025-09-17 22:55:16
(epstopdf)                    size: 547349 bytes
(epstopdf)             Command: <repstopdf --outfile=Fig3-eps-converted-to.pdf Fig3.eps>
(epstopdf)             \includegraphics on input line 471.
Package epstopdf Info: Output file is already uptodate.
<Fig3-eps-converted-to.pdf, id=385, 645.41125pt x 366.36874pt>
File: Fig3-eps-converted-to.pdf Graphic file (type pdf)
<use Fig3-eps-converted-to.pdf>
Package pdftex.def Info: Fig3-eps-converted-to.pdf  used on input line 471.
(pdftex.def)             Requested size: 409.71689pt x 232.57448pt.

LaTeX Warning: `h' float specifier changed to `ht'.

Package epstopdf Info: Source file: <Fig4.eps>
(epstopdf)                    date: 2024-12-20 16:16:02
(epstopdf)                    size: 1074538 bytes
(epstopdf)             Output file: <Fig4-eps-converted-to.pdf>
(epstopdf)                    date: 2025-09-17 22:55:17
(epstopdf)                    size: 507235 bytes
(epstopdf)             Command: <repstopdf --outfile=Fig4-eps-converted-to.pdf Fig4.eps>
(epstopdf)             \includegraphics on input line 480.
Package epstopdf Info: Output file is already uptodate.
<Fig4-eps-converted-to.pdf, id=387, 1041.8925pt x 338.26375pt>
File: Fig4-eps-converted-to.pdf Graphic file (type pdf)
<use Fig4-eps-converted-to.pdf>
Package pdftex.def Info: Fig4-eps-converted-to.pdf  used on input line 480.
(pdftex.def)             Requested size: 409.71689pt x 133.02171pt.

LaTeX Warning: `h' float specifier changed to `ht'.


Underfull \hbox (badness 1325) in paragraph at lines 493--494
[]\OT1/cmr/bx/n/10.03749 DA Adver-sar-ial \OT1/cmr/m/n/10 ($\OML/cmm/m/it/10.03749 D[]$\OT1/cmr/m/n/10 ). The per-for-mance
 []


LaTeX Warning: No positions in optional float specifier.
               Default added (so using `tbp') on input line 496.


Underfull \vbox (badness 10000) has occurred while \output is active []


Underfull \hbox (badness 10000) has occurred while \output is active
[]| []
 []

[9
pdfTeX warning (ext4): destination with the same identifier (name{table.2}) has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.520 
      
pdfTeX warning (ext4): destination with the same identifier (name{table.3}) has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.520 
      ]
Underfull \vbox (badness 10000) has occurred while \output is active []


Underfull \hbox (badness 10000) has occurred while \output is active
[]| []
 []

[10
pdfTeX warning (ext4): destination with the same identifier (name{table.4}) has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.522 
      
pdfTeX warning (ext4): destination with the same identifier (name{figure.3}) has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.522 
       <./Fig3-eps-converted-to.pdf>]

LaTeX Warning: No positions in optional float specifier.
               Default added (so using `tbp') on input line 526.


Underfull \vbox (badness 10000) has occurred while \output is active []


Underfull \hbox (badness 10000) has occurred while \output is active
[]| []
 []

[11
pdfTeX warning (ext4): destination with the same identifier (name{figure.4}) has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.557 I
       n the future work, we hope to apply EEG recognition and generation fo... <./Fig4-eps-converted-to.pdf>]
Underfull \hbox (badness 10000) has occurred while \output is active
[]| []
 []

[12
pdfTeX warning (ext4): destination with the same identifier (name{table.5}) has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.557 I
       n the future work, we hope to apply EEG recognition and generation fo...
pdfTeX warning (ext4): destination with the same identifier (name{table.6}) has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.557 I
       n the future work, we hope to apply EEG recognition and generation fo...]

Package hyperref Warning: Difference (4) between bookmark levels is greater 
(hyperref)                than one, level fixed on input line 563.


Underfull \hbox (badness 1107) in paragraph at lines 563--564
\OT1/cmr/m/n/10 has accom-pa-ny-ing sup-ple-men-tary file/s please
 []


Package hyperref Warning: Difference (3) between bookmark levels is greater 
(hyperref)                than one, level fixed on input line 571.


Underfull \hbox (badness 3009) in paragraph at lines 571--572
\OT1/cmr/m/n/10 Foun-da-tion of China (Grants 62071366 and
 []


Underfull \hbox (badness 2080) in paragraph at lines 571--572
\OT1/cmr/m/n/10 Shaanxi Province (Grant 2025JC-YBMS-702),
 []


Underfull \hbox (badness 1152) in paragraph at lines 575--576
\OT1/cmr/m/n/10 of China (Grants 62071366 and 61671362),
 []


Underfull \hbox (badness 1005) in paragraph at lines 575--576
\OT1/cmr/m/n/10 the Nat-u-ral Sci-ence Foun-da-tion of Shaanxi
 []


Underfull \hbox (badness 3386) in paragraph at lines 575--576
\OT1/cmr/m/n/10 Excel-lent Doc-toral Dis-ser-ta-tion Cul-ti-va-tion
 []


Underfull \hbox (badness 3039) in paragraph at lines 580--581
[]\OT1/cmr/m/n/10 Materials avail-abil-ity: This study did not
 []

(./sn-article.bbl
Underfull \hbox (badness 1163) in paragraph at lines 49--58
\OT1/cmr/m/n/10 learn-ing and deep learn-ing tech-niques in
 []


Underfull \hbox (badness 5592) in paragraph at lines 76--86
\OT1/cmr/m/n/10 Arti-fi-cial Intel-li-gence Review \OT1/cmr/bx/n/10.03749 57\OT1/cmr/m/n/10 (3), 75
 []


Underfull \hbox (badness 3199) in paragraph at lines 108--120
\OT1/cmr/m/n/10 P.-A.: Unsu-per-vised multi-source domain
 []


Underfull \vbox (badness 10000) has occurred while \output is active []


Underfull \hbox (badness 10000) has occurred while \output is active
[]| []
 []

[13{d:/texlive/2024/texmf-dist/fonts/enc/dvips/cm-super/cm-super-ts1.enc}]
Underfull \hbox (badness 1546) in paragraph at lines 124--134
\OT1/cmr/m/n/10 meta-learning frame-work towards opti-mal
 []


Underfull \hbox (badness 1642) in paragraph at lines 157--171
\OT1/cmr/m/n/10 Jafarpisheh, A.S., Ameri, A., Pouyakian,
 []


Underfull \hbox (badness 2846) in paragraph at lines 157--171
\OT1/cmr/m/n/10 M.: Dynamic eeg changes dur-ing expo-
 []


Underfull \hbox (badness 1005) in paragraph at lines 157--171
\OT1/cmr/m/n/10 sure to noise at dif-fer-ent lev-els of loud-
 []


Underfull \hbox (badness 7433) in paragraph at lines 157--171
\OT1/cmr/m/n/10 ness and sharp-ness. Applied Acous-tics
 []


Underfull \hbox (badness 1132) in paragraph at lines 175--188
\OT1/cmr/m/n/10 Kwoh, C.-K., Li, X.: Label-efficient time
 []


Underfull \hbox (badness 2970) in paragraph at lines 175--188
\OT1/cmr/m/n/10 series rep-re-sen-ta-tion learn-ing: A review.
 []


Underfull \hbox (badness 10000) in paragraph at lines 207--216
\OT1/cmr/m/n/10 Farley, D., Mohamed, S.: Vari-a-tional
 []


Underfull \hbox (badness 10000) in paragraph at lines 207--216
\OT1/cmr/m/n/10 Approaches for Auto-Encoding Gen-er-a-
 []


Underfull \hbox (badness 7012) in paragraph at lines 207--216
\OT1/cmr/m/n/10 tive Adver-sar-ial Net-works. Preprint at
 []


Underfull \hbox (badness 10000) in paragraph at lines 220--230
\OT1/cmr/m/n/10 www . frontiersin . org / journals / neuroscience /
 []


Underfull \hbox (badness 2150) in paragraph at lines 234--241
[]\OT1/cmr/m/n/10 Dagdevir, E., Tok-makci, M.: Deter-mi-na-
 []


Underfull \hbox (badness 5787) in paragraph at lines 234--241
\OT1/cmr/m/n/10 for Brain Com-puter Inter-face on BCI
 []


Underfull \hbox (badness 1072) in paragraph at lines 234--241
\OT1/cmr/m/n/10 Com-pe-ti-tion IV Data Set 2b: A Review
 []


Underfull \hbox (badness 3138) in paragraph at lines 234--241
\OT1/cmr/m/n/10 Study. figshare [][]$hthttps : / / doi . org / 10 . 1080 /
 []


Underfull \hbox (badness 1009) in paragraph at lines 261--271
\OT1/cmr/m/n/10 motion image eeg sig-nals based on lmd-
 []


Underfull \hbox (badness 10000) in paragraph at lines 291--300
[]\OT1/cmr/m/n/10 Luo, T.-j.: Selec-tive multi--view time--
 []


Underfull \hbox (badness 3907) in paragraph at lines 291--300
\OT1/cmr/m/n/10 Sys-tems with Appli-ca-tions \OT1/cmr/bx/n/10.03749 247\OT1/cmr/m/n/10 , 123239
 []


Underfull \hbox (badness 2253) in paragraph at lines 291--300
\OT1/cmr/m/n/10 (2024) [][]$https : / / doi . org / 10 . 1016 / j . eswa . 2024 .
 []


Underfull \hbox (badness 7379) in paragraph at lines 321--333
[]\OT1/cmr/m/n/10 Dose, H., M^^\ller, J.S., Iversen, H.K.,
 []


Underfull \hbox (badness 2529) in paragraph at lines 321--333
\OT1/cmr/m/n/10 Puthussery-pady, S.: An end-to-end deep
 []


Underfull \hbox (badness 1377) in paragraph at lines 337--349
[]\OT1/cmr/m/n/10 Cao, J., Li, G., Shen, J., Dai, C.: Ifb-
 []


Underfull \hbox (badness 5667) in paragraph at lines 337--349
\OT1/cmr/m/n/10 clnet: Spatio-temporal fre-quency fea-ture
 []


Underfull \hbox (badness 10000) in paragraph at lines 353--369
[]\OT1/cmr/m/n/10 Schirrmeister, R.T., Sprin-gen-berg,
 []


Underfull \hbox (badness 6792) in paragraph at lines 353--369
\OT1/cmr/m/n/10 J.T., Fiederer, L.D.J., Glasstet-ter, M.,
 []


Underfull \hbox (badness 1210) in paragraph at lines 353--369
\OT1/cmr/m/n/10 decod-ing and visu-al-iza-tion. Human brain
 []


Underfull \vbox (badness 10000) has occurred while \output is active []


Underfull \hbox (badness 10000) has occurred while \output is active
[]| []
 []

[14]
Underfull \hbox (badness 1152) in paragraph at lines 373--384
[]\OT1/cmr/m/n/10 Khan, S.A., Chaudary, E., Mum-taz, W.:
 []


Underfull \hbox (badness 1354) in paragraph at lines 373--384
\OT1/cmr/m/n/10 based subject-dependent emo-tion recog-ni-
 []


Underfull \hbox (badness 2057) in paragraph at lines 388--398
\OT1/cmr/m/n/10 trans-fer. IEEE Sig-nal Pro-cess-ing Let-ters
 []


Underfull \hbox (badness 1888) in paragraph at lines 402--413
\OT1/cmr/m/n/10 com-mon spa-tial pat-tern (ccsp): A novel
 []


Underfull \hbox (badness 1552) in paragraph at lines 402--413
\OT1/cmr/m/n/10 imagery sig-nal. Plos one \OT1/cmr/bx/n/10.03749 16\OT1/cmr/m/n/10 (3), 0248511
 []


Underfull \hbox (badness 3118) in paragraph at lines 465--475
\OT1/cmr/m/n/10 ods for eeg-based emo-tion clas-si-fi-ca-tion.
 []


Underfull \hbox (badness 3482) in paragraph at lines 492--503
[]\OT1/cmr/m/n/10 Raza, H., Cecotti, H., Li, Y., Prasad,
 []


Underfull \hbox (badness 2828) in paragraph at lines 492--503
\OT1/cmr/m/n/10 computer inter-face. Soft Com-put-ing \OT1/cmr/bx/n/10.03749 20\OT1/cmr/m/n/10 ,
 []

)
Underfull \vbox (badness 10000) has occurred while \output is active []


Underfull \hbox (badness 10000) has occurred while \output is active
[]| []
 []

[15] (./sn-article.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********


LaTeX Font Warning: Size substitutions with differences
(Font)              up to 0.985pt have occurred.

Package rerunfilecheck Info: File `sn-article.out' has not changed.
(rerunfilecheck)             Checksum: 3FD7F543B934AA48372084B804FEBBFB;1854.
 ) 
Here is how much of TeX's memory you used:
 14719 strings out of 476065
 223492 string characters out of 5792787
 1958190 words of memory out of 5000000
 36487 multiletter control sequences out of 15000+600000
 586802 words of font info for 147 fonts, out of 8000000 for 9000
 24 hyphenation exceptions out of 8191
 90i,19n,93p,1626b,658s stack positions out of 10000i,1000n,20000p,200000b,200000s
<d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx7.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx8.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx9.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi5.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi7.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi8.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr12.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr17.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr5.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr7.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr8.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr9.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy7.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy8.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/symbols/msbm10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/cm-super/sfrm1000.pfb>
Output written on sn-article.pdf (15 pages, 2397598 bytes).
PDF statistics:
 639 PDF objects out of 1000 (max. 8388607)
 462 compressed objects within 5 object streams
 101 named destinations out of 1000 (max. 500000)
 141 words of extra memory for PDF output out of 10000 (max. 10000000)

